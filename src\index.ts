import * as Repos from './repo/index';
import * as GlobalState from './global-state';
import { HttpAssist } from './modules/http-assit.js';
import { ServerManager } from './modules/base-repo';
import { Try2InitTickTranslator } from './communication/native-message-resolver';

export * from './types/common';
export * from './types/trading';
export * from './types/query';
export * from './types/data-package';
export * from './types/table/admin';
export * from './types/table/account';
export * from './types/table/fund';
export * from './types/table/risk-control';

export * from './config/core';
export * from './config/architecture';
export * from './config/server-function';
export * from './config/server-event';
export * from './config/trading';

export {

    /**
     * HTTP请求助手
     */
    HttpAssist,

    /**
     * 服务器管理单元
     */
    ServerManager,

    /**
     * 各个数据仓库集合
     */
    Repos,

    /**
     * 全局状态集（提供对运行环境信息、用户信息的持续单例供给）
     */
    GlobalState,

    /**
     * 初始化TICK数据解析器
     */
    Try2InitTickTranslator,
};