<script setup lang="ts">
import { computed, defineAsyncComponent, provide, reactive, watch } from 'vue';
import { MenuGroups } from '@/enum/menu';
import { getEmptyTableColumnConfig, type Menu, type TableColumnConfigParam } from '@/types';
import TableColumnConfig from '../common/TableColumnConfig.vue';
import { TABLE_COLUMN_SELECT_KEY } from '@/keys';
import { Utils } from '@/script';

const { activeMenu, include } = defineProps<{
  activeMenu: Menu;
  include: string[];
}>();

const components: Record<string, unknown> = {};
const menus = MenuGroups.map(x => x.menus).flat();
const componentIns = computed(() => components[activeMenu.component!]);

function loadComponent() {
  menus.forEach(menu => {
    if (menu.component) {
      components[menu.component] = defineAsyncComponent(
        () => import(`../../views/HomeView/${menu.component}.vue`),
      );
    } else if (menu.children) {
      menu.children.forEach(child => {
        components[child.component!] = defineAsyncComponent(
          () => import(`../../views/HomeView/${child.component}.vue`),
        );
      });
    }
  });
}

loadComponent();

const tableCfgDialog = reactive({ visible: false, config: getEmptyTableColumnConfig() });
const tableCfgParams = reactive<TableColumnConfigParam>(getEmptyTableColumnConfig());
provide(TABLE_COLUMN_SELECT_KEY, tableCfgParams);
watch(() => tableCfgParams, openTableCfgDialog, { deep: true });

function openTableCfgDialog() {
  const cloned = Utils.deepClone(tableCfgParams);
  console.log('openTableCfgDialog', cloned);
  tableCfgDialog.config = Utils.deepClone(cloned);
  tableCfgDialog.visible = true;
}

function cfgChanged() {
  console.log('cfgChanged');
  tableCfgDialog.visible = false;
}

function cfgCancel() {
  console.log('cfgCancel');
  tableCfgDialog.visible = false;
}
</script>

<template>
  <div class="home-content-view">
    <KeepAlive :max="10" :include="include">
      <component :is="componentIns"></component>
    </KeepAlive>
    <TableColumnConfig
      :visible="tableCfgDialog.visible"
      :config="tableCfgDialog.config"
      @change="cfgChanged"
      @canceled="cfgCancel"
    ></TableColumnConfig>
  </div>
</template>

<style>
.home-content-view {
  width: 100%;
  height: 100%;

  > * {
    width: 100%;
    height: 100%;
  }
}
</style>
