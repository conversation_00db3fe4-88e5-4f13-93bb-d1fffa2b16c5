<template>
  <el-dialog title="配置流程" v-model:visible="dialogVisible">
    <!-- 投顾配置 -->
    <div class="config-box">
      <h3>投顾配置</h3>
      <el-form label-width="120px">
        <el-form-item label="允许发起投资决策">
          <el-select v-model="investmentDecision" placeholder="请选择">
            <el-option label="允许发起投资决策" value="allow"></el-option>
            <el-option label="不允许发起投资决策" value="disallow"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="离线自动驳回">
          <el-select v-model="offlineRejection" placeholder="请选择">
            <el-option label="离线自动驳回" value="autoReject"></el-option>
            <el-option label="离线暂停/离线驳回" value="pauseReject"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择投顾">
          <el-checkbox-group v-model="selectedAdvisors">
            <el-checkbox v-for="advisor in advisors" :key="advisor" :label="advisor">
              {{ advisor }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
    </div>

    <!-- 交易员配置 -->
    <div class="config-box">
      <h3>交易员配置</h3>
      <el-form label-width="120px">
        <el-form-item label="不允许发起投资决策">
          <el-input v-model="traderDecision" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="离线暂停/离线驳回">
          <el-select v-model="traderOfflineAction" placeholder="请选择">
            <el-option label="离线暂停/离线驳回" value="pauseReject"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择交易员">
          <el-checkbox-group v-model="selectedTraders">
            <el-checkbox v-for="trader in traders" :key="trader" :label="trader">
              {{ trader }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
    </div>

    <div style="text-align: right">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="save">保存</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';

export default defineComponent({
  name: 'ProductWorkflowSetting',
  setup() {
    const dialogVisible = ref(true);
    const investmentDecision = ref('allow');
    const offlineRejection = ref('autoReject');
    const selectedAdvisors = ref(['张三', '李四']);
    const advisors = ['张三', '李四', '王五'];
    const traderDecision = ref('');
    const traderOfflineAction = ref('pauseReject');
    const selectedTraders = ref(['张三', '李四']);
    const traders = ['张三', '李四', '王五'];

    const cancel = () => {
      // 取消逻辑
    };

    const save = () => {
      // 保存逻辑
    };

    return {
      dialogVisible,
      investmentDecision,
      offlineRejection,
      selectedAdvisors,
      advisors,
      traderDecision,
      traderOfflineAction,
      selectedTraders,
      traders,
      cancel,
      save,
    };
  },
});
</script>

<style scoped>
.config-box {
  margin-bottom: 20px;
}
</style>
