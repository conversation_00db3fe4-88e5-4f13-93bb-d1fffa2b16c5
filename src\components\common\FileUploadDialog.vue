<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import { ElDialog, ElButton, ElProgress } from 'element-plus';
import type { FileUploadDialogServiceOption } from '@/types/components/basic';

const props = defineProps<FileUploadDialogServiceOption>();
const dialogVisible = ref(true);
const dialogWidth = ref<number | string>('550px');

const hasTitle = computed(() => {
  return !!props.title;
});

const chooseButtonText = computed(() => {
  return props.confirmText || '浏览文件上传';
});

const cancelButtonText = computed(() => {
  return props.cancelText || '取消上传';
});

const states = reactive({
  status: 'ready' as 'ready' | 'uploading',
  progress: 20,
  files: [] as { fileName: string }[],
});

const isReady = computed(() => {
  return states.status === 'ready';
});

const isUploading = computed(() => {
  return states.status === 'uploading';
});

function handleChoose() {
  if (props.onChoose) {
    props.onChoose();
  }
}

function handleUpload() {
  if (props.onUpload) {
    props.onUpload();
  }
}

function handleCancel() {
  if (props.onCancel) {
    props.onCancel();
  }
}

const handleClose = (done: () => void) => {
  done();
  handleCancel();
};
</script>

<template>
  <el-dialog
    class="file-upload-dialog"
    v-model="dialogVisible"
    :width="dialogWidth"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    :before-close="handleClose"
  >
    <template v-if="hasTitle" #header>
      <div class="dialog-header">
        <slot name="header">
          <div fs-18 fw-400>{{ title || '文件上传' }}</div>
        </slot>
      </div>
    </template>
    <div class="dialog-content">
      <slot name="content">
        <div v-if="isUploading" class="upload-guide" flex flex-col jcc aic>
          <i class="iconfont icon-loading file-upload-loading-icon" fs-32></i>
          <div mt-16 fs-18 fw-400>
            <span>{{ '上传中...' }}</span>
          </div>
          <div w-full mt-16>
            <el-progress
              :percentage="states.progress"
              :show-text="false"
              :stroke-width="8"
              striped
              striped-flow
            />
          </div>
          <div mt-12>
            <span>{{ (states.progress || 0).toFixed(2) }}% 完成</span>
          </div>
        </div>
        <div v-else class="upload-guide ready" flex flex-col jcc aic>
          <i class="iconfont icon-upload" fs-48></i>
          <div mt-16 fs-18 fw-400>
            <span>{{ message || '请选择要上传的文件！' }}</span>
            <span v-if="fileTypes && fileTypes.length > 0">
              (支持文件类型：{{ fileTypes.join('、') }})
            </span>
          </div>
        </div>
      </slot>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <slot name="footer">
          <div flex jcc gap-10 mt-10>
            <el-button type="primary" @click="handleChoose" :disabled="isUploading">
              <i class="iconfont icon-file-open"></i>
              <span p-l-5>{{ chooseButtonText }}</span>
            </el-button>
            <el-button
              v-if="isReady && states.files.length > 0"
              type="primary"
              @click="handleUpload"
            >
              <i class="iconfont icon-upload"></i>
              <span p-l-5>开始上传</span>
            </el-button>
            <el-button type="default" @click="handleCancel">
              <i class="iconfont icon-close"></i>
              <span p-l-5>{{ cancelButtonText }}</span>
            </el-button>
          </div>
        </slot>
      </div>
    </template>
  </el-dialog>
</template>

<style>
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.file-upload-loading-icon {
  /* 让图标按设定的时间完成一次360度旋转 */
  animation: spin 1s linear infinite;
}

.file-upload-dialog {
  border-radius: 16px !important;

  .dialog-content {
    .msg-text {
      color: white;
      opacity: 0.7;
    }
  }

  .upload-guide {
    padding-top: 12px;
    padding-bottom: 12px;

    &.ready {
      border: 2px dashed var(--g-text-color-2);
    }
  }

  .dialog-footer {
    text-align: right;

    .button-row {
      height: 44px;
      display: flex;

      > button {
        width: 100%;
        height: 100%;
        font-size: 16px;
        font-weight: 600;
      }
    }
  }
}
</style>
