function getMockData() {
  const data: any[] = [];

  for (let i = 0; i < 100; i++) {
    data.push({
      id: i + 1,
      orgName: `机构 ${i + 1}`,
      domain: `domain${i + 1}.com`,
      contract: `Contact Person ${i + 1}`,
      phone: `123-456-789${i}`,
      email: `org${i + 1}@example.com`,
      status: i % 2 === 0 ? 1 : 0,
      introduction: `Introduction for 机构 ${i + 1}`,
      createTime: Date.now() - Math.floor(Math.random() * 1000000000),
      updateTime: Date.now() - Math.floor(Math.random() * 100000000),
      configuration: JSON.stringify({ key: `value${i}` }),
    });
  }

  return data;
}

// Example usage
const data = getMockData();
export default data;
