<script setup lang="ts">
import { ref, shallowRef, computed, provide } from 'vue';
import TradeChannels from '@/components/NormalTradeView/TradeChannels.vue';
import ComponentTabs from '@/components/common/ComponentTabs.vue';
import AccountDetail from '@/components/NormalTradeView/AccountDetail.vue';
import TradeTabs from '@/components/NormalTradeView/TradeTabs.vue';
import type { FinanceAccountDetail, AnyObject, ComponentTab, TradeChannel } from '@/types';
import { TRADE_CHANNELS } from '@/enum/trade';
import { INSTRUMENT_SELECT_KEY } from '@/keys';

// 选中合约，依赖注入到InstrumentInput组件中
const instrumentSelect = ref<string>('');
provide(INSTRUMENT_SELECT_KEY, instrumentSelect);

const activeAccount = ref<FinanceAccountDetail>();

const topTabs = computed<ComponentTab[]>(() => [
  {
    label: '持仓',
    component: 'CommonPositions',
    props: {
      activeItem: activeAccount.value,
      type: 'account',
    },
    events: {
      'row-dblclick': rowDblClick,
    },
  },
]);

const activeChannel = shallowRef<TradeChannel>(TRADE_CHANNELS[0]);

const bottomTabs = computed<ComponentTab[]>(() => [
  {
    label: '委托',
    component: 'CommonOrders',
    props: {
      activeItem: activeAccount.value,
      type: 'account',
    },
    events: {
      'row-dblclick': rowDblClick,
    },
  },
  {
    label: '成交',
    component: 'CommonRecords',
    props: {
      activeItem: activeAccount.value,
      type: 'account',
    },
    events: {
      'row-dblclick': rowDblClick,
    },
  },
]);

const rowDblClick = (rowData: { instrument: string } & AnyObject) => {
  instrumentSelect.value = rowData.instrument;
};
</script>

<template>
  <div flex flex-col>
    <TradeChannels v-model="activeChannel" />
    <div flex flex-1 min-h-1>
      <div w-480 mr-8 bg="[--g-panel-bg]" flex="~ col">
        <AccountDetail :active-channel="activeChannel" v-model="activeAccount" />
        <TradeTabs :active-channel="activeChannel" :active-account="activeAccount" flex-1 min-h-1 />
      </div>
      <div flex="~ col" flex-1 min-w-1>
        <ComponentTabs flex-1 min-h-1 :tabs="topTabs" />
        <ComponentTabs flex-1 min-h-1 :tabs="bottomTabs" />
      </div>
    </div>
  </div>
</template>

<style scoped></style>
