<script setup lang="ts">
import { MenuGroups } from '@/enum/menu';
import { type Menu } from '@/types';
import { ref } from 'vue';

defineProps<{
  activeMenu: Menu;
}>();

const collapse = ref(false);
const defaultOpeneds = MenuGroups.map(item => item.menus)
  .flat()
  .map(x => x.name);

const emit = defineEmits<{
  clickMenu: [item: Menu];
}>();

const handleClickMenu = (item: Menu) => {
  emit('clickMenu', item);
};
</script>

<template>
  <div class="home-menu" bg="[--g-bg-1]" h-full py-20>
    <el-scrollbar>
      <div v-for="(group, group_idx) in MenuGroups" :key="group_idx" class="group-box">
        <div class="group-title" px-20 fw-400 lh-16>{{ group.name }}</div>
        <el-menu
          w-200
          class="el-menu-home"
          :default-active="activeMenu.component"
          :default-openeds="defaultOpeneds"
          :collapse="collapse"
        >
          <template v-for="item in group.menus">
            <el-sub-menu v-if="item.children" :key="item.name" :index="item.name">
              <template #title>
                <div flex aic>
                  <i fs-14 block pr-5 pt-2 class="iconfont" :class="`icon-${item.icon}`"></i>
                  {{ item.name }}
                </div>
              </template>
              <el-menu-item
                v-for="sub in item.children"
                :key="sub.component"
                :index="sub.component"
                @click="handleClickMenu(sub)"
              >
                {{ sub.name }}
              </el-menu-item>
            </el-sub-menu>
            <el-menu-item
              v-else
              :key="item.component"
              :index="item.component!"
              @click="handleClickMenu(item)"
            >
              <div flex aic>
                <i fs-14 block pr-5 pt-2 class="iconfont" :class="`icon-${item.icon}`"></i>
                {{ item.name }}
              </div>
            </el-menu-item>
          </template>
        </el-menu>
      </div>
    </el-scrollbar>
  </div>
</template>

<style scoped>
.home-menu {
  .group-box {
    &:not(:first-child) {
      .group-title {
        margin-top: 48px;
      }
    }
  }

  :deep() {
    .el-menu {
      .el-menu-item {
        margin-top: 16px;
        font-size: 16px;
        font-weight: 600px;

        .iconfont {
          position: relative;
          top: -2px;
          font-size: 24px;
        }
      }
    }
  }
}
</style>
