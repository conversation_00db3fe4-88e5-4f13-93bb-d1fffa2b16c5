import { BaseRepo } from '../modules/base-repo';
import { AxiosXHR } from '../modules/http-assit';
import { ServerFunction } from '../config/server-function';
import { ServerEvent } from '../config/server-event';
import { TickType } from '../config/trading';
import { GetLogger } from '../global-state';
import { HttpResponseData, StandardTick, TickCallbackMethod, TickType2CallbackParamMap } from '../types/common';
import { SocketDataPackage } from '../types/data-package';
import { MessageDataType } from '../config/architecture';
import { TickHttpHelper } from './tick-http';

const defaultLogger = GetLogger();

/**
 * 组装TICK数据订阅参数结构
 * 该参数对象，用于查询某合约不同类型盘口数据
 * 这些数据类型包括ticks、simpleTicks、transactions、orders、bars和frontOrders
 */
function AssembleSubParam(instrument: string, tickType: TickType) {

    const param = {

        simpleTicks: [] as string[],
        ticks: [] as string[],
        bars: [] as string[],
        transactions: [] as string[],
        orders: [] as string[],
        frontOrders: [] as string[],
    };

    switch (tickType) {

        case TickType.simple: param.simpleTicks.push(instrument); break;
        case TickType.tick: param.ticks.push(instrument); break;
        case TickType.kline: param.bars.push(instrument); break;
        case TickType.transaction: param.transactions.push(instrument); break;
        case TickType.queue: param.orders.push(instrument); break;
        case TickType.front: param.frontOrders.push(instrument); break;
    }
    
    return param;
}

/**
 * 创建订阅键
 */
function createSubscribeKey(instrument: string, tickType: TickType) {
    return `${instrument}_${tickType}`;
}

/**
 * 存活着的订阅
 */
const AliveSubscribeMap: { [instrument_tick_type: string]: Array<TickCallbackMethod<any>> } = {};

const states = {

    /**
     * 是否已经建立TICK数据监听
     */
    hasListened2Tick: false
};

export class TickRepo extends BaseRepo {

    private tickHelper: TickHttpHelper;

    constructor() {

        super();
        this.tickHelper = new TickHttpHelper(this.handleHttpTick.bind(this));
    }
    
    /**
     * 查询某类合约全量的当前时刻最新价格
     * @param assetType - 资产类型
     */
    async QuerySimpleTick(assetType: string) {
        throw new Error('not implemented');
    }

    /**
     * 查询指定合约当前时刻的TICK数据
     * @param instruments - 合约数组
     */
    async QueryTick(instruments: string[]) {
        throw new Error('not implemented');
    }

    /**
     * 处理来自HTTP接口的TICK行情
     */
    handleHttpTick(resp: HttpResponseData<StandardTick>) {

        const { errorCode, errorMsg, data } = resp;
        const fc = ServerEvent.SubscribeTickReceived;
        const message: SocketDataPackage<StandardTick> = {

            fc,
            fcEvent: ServerEvent[fc],
            reqId: 0,
            dataType: MessageDataType.json,
            body: data,
        };

        this.qserver.injectMessage(fc, message);
    }

    /**
     * 订阅合约TICK行情
     * @param instrument - 合约
     * @param tickType - 行情类型
     */
    SubscribeTick<T extends keyof TickType2CallbackParamMap>(instrument: string, tickType: T, callback: TickCallbackMethod<T>): void {

        if (!instrument) {
            throw new Error('Instrument cannot be empty.');
        }

        if (typeof callback !== 'function') {
            throw new Error('Callback cannot be empty.');
        }

        if (!states.hasListened2Tick) {

            states.hasListened2Tick = true;
            this.listen2TickEvents();
        }

        const key = createSubscribeKey(instrument, tickType);
        const subscribers = AliveSubscribeMap[key] || [];

        if (subscribers.includes(callback)) {
            defaultLogger.log('Tick is already subscribed by this callback', instrument);
        }

        subscribers.push(callback);
        AliveSubscribeMap[key] = subscribers;

        if (subscribers.length === 1) {

            if (tickType === TickType.tick) {
                this.tickHelper.Subscribe(instrument);
            }
            else {
                
                const param = AssembleSubParam(instrument, tickType);
                const msg = { fc: ServerFunction.SubscribeTick, dataType: MessageDataType.json, body: param };
                this.qserver.send(msg);
            }
        }
    }

    /**
     * 退订合约TICK行情
     * @param instrument - 合约
     * @param tickType - 行情类型
     */
    UnsubscribeTick<T extends keyof TickType2CallbackParamMap>(instrument: string, tickType: T, callback: TickCallbackMethod<T>): void {

        if (!instrument) {
            throw new Error('Instrument cannot be empty.');
        }

        if (typeof callback !== 'function') {
            throw new Error('Callback cannot be empty.');
        }

        const key = createSubscribeKey(instrument, tickType);
        const subscribers = AliveSubscribeMap[key] || [];

        if (!subscribers.includes(callback)) {
            defaultLogger.log('Tick is not subscribed by this callback', instrument);
        }

        const index = subscribers.indexOf(callback);
        if (index > -1) {
            subscribers.splice(index, 1);
        }

        if (subscribers.length === 0) {

            if (tickType === TickType.tick) {

                this.tickHelper.Unsubscribe(instrument);
                const fc = ServerEvent.UnsubscribeTickReceived;
                const message: SocketDataPackage = {

                    fc,
                    fcEvent: ServerEvent[fc],
                    reqId: 0,
                    dataType: MessageDataType.text,
                    body: '',
                };

                this.qserver.injectMessage(fc, message);
            }
            else {
                
                delete AliveSubscribeMap[key];
                const param = AssembleSubParam(instrument, tickType);
                const msg = { fc: ServerFunction.UnsubscribeTick, dataType: MessageDataType.json, body: param };
                this.qserver.send(msg);
            }
        } 
        else {
            AliveSubscribeMap[key] = subscribers;
        }
    }

    private listen2TickEvents() {

        const thisObj = this;
        function handleTickPush(data: SocketDataPackage<StandardTick | StandardTick[]>) {

            defaultLogger.trace('Received a TickPush message', data);
            thisObj.handleTickPush(data.body!);
        }
        
        function handleTickUnsubscribeReply(data: SocketDataPackage) {
            defaultLogger.trace('Received a TickUnsubscribeReply message', data);
        }

        this.qserver.subscribe(ServerEvent.TickPriceChanged, handleTickPush);
        this.qserver.subscribe(ServerEvent.SubscribeTickReceived, handleTickPush);
        this.qserver.subscribe(ServerEvent.UnsubscribeTickReceived, handleTickUnsubscribeReply);
    }

    /**
     * 处理行情推送
     * @param data - 行情数据
     */
    private handleTickPush(data: StandardTick | StandardTick[]) {

        const ticks = Array.isArray(data) ? data : [data];
        ticks.forEach(item => {

            const { instrumentID } = item;
            const key = createSubscribeKey(instrumentID, TickType.tick);
            const subscribers = AliveSubscribeMap[key] || [];
    
            for (const callback of subscribers) {
    
                try {
                    callback(item);
                } 
                catch (error) {
                    defaultLogger.error(`Error invoking callback for instrument ${instrumentID}:`, error);
                }
            }
        });
    }
}