import { GetEnv } from '../global-state';
import { HttpAssist } from './http-assit';
import { RuntimeEnvironment } from '../config/architecture';
import { CommunicationFactory } from '../communication/factory';
import { ICommunication } from '../communication/protocol';

const ServerState = {

    /** 交易服务器 */
    TradeServer: { host: '', port: 0, instance: null as ICommunication | null },
    /** 行情服务器 */
    QuoteServer: { host: '', port: 0, instance: null as ICommunication | null },
};

/**
 * 设置交易服务器（全局唯一实例）
 */
async function SetTradeServer(host: string, port: number) {

    Object.assign(ServerState.TradeServer, { host, port });
    const env = GetEnv();
    ServerState.TradeServer.instance = await CommunicationFactory.CreateTradeServer(env, host, port);
}

/**
 * 获取交易服务器（全局唯一实例）
 */
function GetTradeServer() {

    const { host, port } = ServerState.TradeServer;
    return { host, port };
}
	
/**
 * 设置行情服务器（全局唯一实例）
 */
async function SetQuoteServer(host: string, port: number) {

    Object.assign(ServerState.QuoteServer, { host, port });
    const env = GetEnv();
    ServerState.QuoteServer.instance = await CommunicationFactory.CreateQuoteServer(env, host, port);
}

/**
 * 获取行情服务器（全局唯一实例）
 */
function GetQuoteServer() {

    const { host, port } = ServerState.QuoteServer;
    return { host, port };
}

/**
 * Socket服务器管理功能
 */
export const ServerManager = {

    SetTradeServer,
    GetTradeServer,
    SetQuoteServer,
    GetQuoteServer,
};

export class BaseRepo {

    /**
     * HTTP助手
     */
    protected get assist() {
        return HttpAssist;
    }

    /**
     * 运行环境标识
     */
    protected get env() {
        return GetEnv();
    }

    /**
     * 是否纯WEB
     */
    protected get isWeb() {
        return this.env == RuntimeEnvironment.Web;
    }

    /**
     * 是否WebSocket环境
     */
    protected get isWebSocket() {
        return this.env == RuntimeEnvironment.WebSocket;
    }

    /**
     * 是否本地客户端环境
     */
    protected get isNativeSocket() {
        return this.env == RuntimeEnvironment.Native;
    }

    /**
     * 是否Socket环境
     */
    protected get isSocket() {
        return this.isWebSocket || this.isNativeSocket;
    }

    /**
     * 交易服务器（从任何Repo子类获取，均为同一服务器实例）
     */
    protected get tserver() {
        return ServerState.TradeServer.instance!;
    }

    /**
     * 行情服务器（从任何Repo子类获取，均为同一服务器实例）
     */
    protected get qserver() {
        return ServerState.QuoteServer.instance!;
    }

    constructor() {
        //
    }
}