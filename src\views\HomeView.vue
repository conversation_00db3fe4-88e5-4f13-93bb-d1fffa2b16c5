<script setup lang="ts">
import HomeMenu from '@/components/HomeView/HomeMenu.vue';
import HomeTitle from '@/components/HomeView/HomeTitle.vue';
import HomeTabs from '@/components/HomeView/HomeTabs.vue';
import HomeToolkit from '@/components/HomeView/HomeToolkit.vue';
import HomeContent from '@/components/HomeView/HomeContent.vue';
import BottomBar from '@/components/HomeView/BottomBar.vue';
import { computed, onBeforeMount, reactive, ref, watch } from 'vue';
import { MenuGroups } from '@/enum/menu';
import { type Menu } from '@/types';
import { Utils } from '@/script';

const firstMenu = MenuGroups[0].menus[0];
const activeMenu = ref<Menu>(firstMenu);
const activeMenus = ref<Menu[]>([firstMenu]);

const activeMenuNames = computed(() => {
  return activeMenus.value.map(item => item.component!);
});

const states = reactive({
  showMenu: true,
});

watch(activeMenu, (newValue, oldValue) => {
  if (newValue.component !== oldValue?.component) {
    Utils.setLocal('activeMenu', newValue);
  }
});

watch(
  () => activeMenus.value.length,
  () => {
    Utils.setLocal('activeMenus', activeMenus.value);
  },
);

// 初始化tabs和当前菜单
onBeforeMount(() => {
  const localActiveMenu = Utils.getLocal<Menu>('activeMenu');
  if (localActiveMenu) {
    activeMenu.value = localActiveMenu;
  }
  const localActiveMenus = Utils.getLocal<Menu[]>('activeMenus');
  if (localActiveMenus) {
    activeMenus.value = localActiveMenus;
  }
});

const handleClickMenu = (menu: Menu) => {
  activeMenu.value = menu;
  if (!activeMenus.value.some(item => item.component === menu.component)) {
    activeMenus.value.push(menu);
  }
};

const handleCloseTab = (menu: Menu) => {
  const menuIndex = activeMenus.value.findIndex(item => item.component === menu.component);
  if (menu.component === activeMenu.value.component) {
    activeMenu.value = activeMenus.value[menuIndex - 1] || activeMenus.value[menuIndex + 1];
  }
  activeMenus.value.splice(menuIndex, 1);
};

function toggleShowMenu() {
  states.showMenu = !states.showMenu;
}
</script>

<template>
  <div class="home-view-root" h-full flex flex-col>
    <div flex aic jcsb px-20 h-68 bg="[--g-block-bg-5]">
      <div class="home-title-box">
        <HomeTitle @toggle="toggleShowMenu" />
      </div>
      <div class="home-tabs-box" flex flex-1 min-w-1>
        <HomeTabs
          :active-menu="activeMenu"
          :tabs="activeMenus"
          @click-menu="handleClickMenu"
          @close-tab="handleCloseTab"
        />
      </div>
      <div class="home-toolkit-box">
        <HomeToolkit />
      </div>
    </div>
    <div flex flex-1 min-h-1 overflow-hidden>
      <HomeMenu v-show="states.showMenu" :active-menu="activeMenu" @click-menu="handleClickMenu" />
      <div class="home-content-box" ml-4 flex-1 min-w-1 flex flex-col>
        <HomeContent :include="activeMenuNames" :active-menu="activeMenu" />
      </div>
    </div>
    <BottomBar />
  </div>
</template>

<style scoped></style>
