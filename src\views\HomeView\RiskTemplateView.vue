<script setup lang="ts">
import TemplateList from '@/components/RiskTemplateView/TemplateList.vue';
import IndicatorTree from '@/components/RiskTemplateView/IndicatorTree.vue';
import IndicatorList from '@/components/RiskTemplateView/IndicatorList.vue';
import CommonSharedIndicator from '@/components/RiskTemplateView/IndicatorConfig/CommonSharedIndicator.vue';
import ApplyAssetScope from '@/components/RiskTemplateView/IndicatorConfig/ApplyAssetScope.vue';
import type { RiskIndicator } from '../../../../xtrade-sdk/dist';
import { ref, useTemplateRef } from 'vue';
import { ElMessage } from 'element-plus';

const isIndicatorCollapsed = ref(false);
const $tree = useTemplateRef('$tree');

function handleTemplateChange(template: any) {
  console.log(template);
  // todo25
}

function handleNodeSelect(indicator: any) {
  console.log(indicator);
  // todo25
}

function handleAddRequest(callback: (current: RiskIndicator, selecteds: RiskIndicator[]) => void) {
  const selecteds = $tree.value!.getSelcteds();
  const current = $tree.value!.getCurrent();

  if (!current) {
    return ElMessage.error('您为选择需要添加的指标');
  }

  callback(current, selecteds);
}

function handleToggle() {
  isIndicatorCollapsed.value = !isIndicatorCollapsed.value;
}
</script>

<template>
  <div class="risk-template-view" flex>
    <div w-242 class="block">
      <TemplateList @select="handleTemplateChange" @toggle="handleToggle" />
    </div>
    <div w-248 class="block" v-show="!isIndicatorCollapsed">
      <IndicatorTree ref="$tree" @select="handleNodeSelect" />
    </div>
    <div w-100 flex-1 flex flex-col>
      <div flex-1 min-h-100 jcc aic>
        <IndicatorList @add="handleAddRequest"></IndicatorList>
      </div>
      <div h-400 flex>
        <div h-full w-540 overflow-x-auto>
          <CommonSharedIndicator></CommonSharedIndicator>
        </div>
        <div h-full flex-1>
          <ApplyAssetScope></ApplyAssetScope>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.block {
  background-color: var(--g-block-bg-2);
}
</style>
