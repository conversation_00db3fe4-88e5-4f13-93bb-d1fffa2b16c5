<script setup lang="ts">
import { TRADE_DIRECTIONS } from '@/enum/trade';
// 使用defineModel实现双向绑定
const direction = defineModel<number>();
</script>

<template>
  <div class="trade-tabs" relative>
    <el-tabs v-model="direction" type="card">
      <el-tab-pane
        v-for="item in TRADE_DIRECTIONS"
        :key="item.value"
        :label="item.label"
        :name="item.value"
      />
    </el-tabs>
    <div absolute w-full bottom-0 left-0 h-1 bg="[var(--g-panel-bg)]"></div>
  </div>
</template>

<style scoped>
.trade-tabs {
  .el-tabs {
    background-color: var(--g-panel-bg3);
  }
  :deep() {
    .el-tabs__header {
      .el-tabs__nav-wrap {
        .el-tabs__nav-scroll {
          .el-tabs__nav {
            .el-tabs__item {
              padding: 0 26px;
              border: none;
              &.is-active {
                background-color: var(--g-panel-bg);
              }
            }
          }
        }
      }
    }
  }
}
</style>
