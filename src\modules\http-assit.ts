import HttpModule, { ConfigHttpRequest } from './http';
import { AnyObject, HttpResponseData } from '../types/common';

export interface AxiosXHR<T = any> {

    config: any;
    headers: {[index: string]: any};
    status: number;
    statusText: string | null;
    result: HttpResponseData<T>;
}

export class HttpAssist {

    /**
     * 设置HTTP接口地址（仅在系统加载之初设置一次）
     */
    static SetHttpBaseUrl(baseUrl: string) {
        ConfigHttpRequest(baseUrl);
    }

    static async Get<T = any>(url: string, params?: AnyObject) {

        try {
            const res = await HttpModule.AxiosInstance.get<HttpResponseData<T>>(url, { params: params || {} });
            return res.data;
        } catch (error) {
            return {
                errorCode: -1,
                errorMsg: ''
            }
        }
    }

    static async Post<T = any>(url: string, params?: AnyObject, data?: any) {

        try {
            const res = await HttpModule.AxiosInstance.post<HttpResponseData<T>>(url, data || undefined, { params: params || {} });
            return res.data;
        } catch (error) {
            return {
                errorCode: -1,
                errorMsg: ''
            }
        }
    }

    static async Put<T = any>(url: string, params?: AnyObject, data?: any) {

        try {
            const res = await HttpModule.AxiosInstance.put<HttpResponseData<T>>(url, data || undefined, { params: params || {} });
            return res.data;
        } catch (error) {
            return {
                errorCode: -1,
                errorMsg: ''
            }
        }
    }

    static async Delete<T = any>(url: string, params?: AnyObject) {

        try {
            const res = await HttpModule.AxiosInstance.delete<HttpResponseData<T>>(url, { params: params || {} });
            return res.data;
        } catch (error) {
            return {
                errorCode: -1,
                errorMsg: ''
            }
        }
    }
}
