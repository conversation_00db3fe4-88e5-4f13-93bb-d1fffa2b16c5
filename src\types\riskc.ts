/**
 * 指标触发项
 */
export interface IndicatorTrigger {
  /**
   * 比较符
   */
  comparer: number;

  /**
   * 阈值
   */
  threshold: number;

  /**
   * 阈值单位
   */
  threholdUnit: string;

  /**
   * 触发项采取动作：1预警 2阻止
   */
  action: number;
}

/**
 * 指标配置
 */
export interface CommonIndicatorConfig {
  /**
   * 指标名称
   */
  name: string;

  /**
   * 指标设置
   */
  setting: {
    direction: number;
    triggers: IndicatorTrigger[];
  };

  /**
   * 控制环节
   */
  stepControl: {
    /**
     * 1针对指令 2针对委托
     */
    target: number;

    /**
     * 循环检查频率（单位秒S）
     */
    frequency: number;
  };

  /**
   * 生效日期
   */
  effectiveDate: {
    /**
     * 开始日期
     */
    start: string;

    /**
     * 结束日期
     */
    end: string;
  };

  /**
   * 生效时间
   */
  effectiveTime: {
    /**
     * 开始时间
     */
    start: string;

    /**
     * 结束时间
     */
    end: string;
  };
}
