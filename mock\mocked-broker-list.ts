function getMockData() {
  const data: any[] = [];

  for (let i = 0; i < 100; i++) {
    data.push({
      id: i + 1,
      brokerId: `经纪商${i + 1}`,
      brokerName: `经纪商 ${i + 1}`,
      brokerType: Math.floor(Math.random() * 3) + 1,
      servers: JSON.stringify([
        `server${Math.floor(Math.random() * 5) + 1}.example.com`,
        `server${Math.floor(Math.random() * 5) + 1}.example.com`,
      ]),
      createTime: Date.now() - Math.floor(Math.random() * 1000000000),
      updateTime: Date.now() - Math.floor(Math.random() * 100000000),
      description: `Description for Broker ${i + 1}`,
    });
  }

  return data;
}

// Example usage
const data = getMockData();
export default data;
