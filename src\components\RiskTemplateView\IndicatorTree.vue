<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue';
import type { TreeInstance } from 'element-plus';
import { ElTree } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import type { RiskIndicator } from '../../../../xtrade-sdk/dist';
import RiskControlService from '@/api/risk-control';

interface TreeData extends RiskIndicator {
  children?: TreeData[];
}

// 根据设计图生成树形数据
const treeNodes = ref<TreeData[]>();

function buildTree(data: RiskIndicator[]): TreeData[] {
  const map = new Map<string, TreeData>();
  data.forEach(item => map.set(item.indicatorCode, { ...item, children: [] }));
  const tree: TreeData[] = [];

  data.forEach(item => {
    const node = map.get(item.indicatorCode);
    if (node) {
      if (!item.parentIndicatorCode) {
        tree.push(node);
      } else {
        const parent = map.get(item.parentIndicatorCode);
        if (parent) {
          parent.children?.push(node);
        }
      }
    }
  });

  return tree;
}

async function request() {
  const indicators = await RiskControlService.getIndicators();
  treeNodes.value = buildTree(indicators);
}

const emitter = defineEmits<{
  select: [item: RiskIndicator];
}>();

const filterText = ref('');
const treeRef = ref<TreeInstance>();

watch(
  () => filterText.value,
  value => {
    treeRef.value!.filter(value);
  },
);

const filterNode = (value: string, data: TreeData) => {
  if (!value) return true;
  return data.indicatorName.includes(value);
};

function handleClick(item: RiskIndicator) {
  emitter('select', item);
}

function getSelcteds() {
  return treeRef.value!.getCheckedNodes(true, false) as RiskIndicator[];
}

function getCurrent() {
  return treeRef.value!.getCurrentNode() as RiskIndicator;
}

onMounted(() => {
  request();
});

defineExpose({
  getCurrent,
  getSelcteds,
});
</script>

<template>
  <div class="tree-control" p-10>
    <!-- 搜索框 -->
    <el-input v-model.trim="filterText" placeholder="搜索指标" :suffix-icon="Search" clearable />
    <!-- 树形结构 -->
    <el-tree
      ref="treeRef"
      :data="treeNodes"
      node-key="id"
      empty-text="无指标数据"
      :props="{ label: 'indicatorName', children: 'children' }"
      :filter-node-method="filterNode as any"
      :show-checkbox="false"
      @node-click="handleClick"
      highlight-current
      default-expand-all
    ></el-tree>
  </div>
</template>

<style scoped>
.tree-control {
  :deep() {
    .el-tree-node__label {
      font-size: 14px;
      font-weight: 400;
      color: var(--g-text-color-2);
    }

    .el-tree-node__content:hover {
      background-color: var(--g-block-bg-6);
    }

    .el-tree-node {
      &.is-current {
        > .el-tree-node__content {
          background-color: var(--g-block-bg-6) !important;
        }
      }
    }
  }
}
</style>
