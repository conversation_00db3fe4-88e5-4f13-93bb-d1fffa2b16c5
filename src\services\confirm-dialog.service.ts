import { h, render } from 'vue';
import ConfirmDialog from '../components/common/ConfirmDialog.vue';
import type { ConfirmDialogOption, ConfirmDialogServiceOption } from '@/types/components/basic';

/**
 * 显示自定义确认对话框
 */
export function showCustomConfirm(options: ConfirmDialogOption): Promise<boolean> {
  return new Promise(resolve => {
    const container = document.createElement('div');
    document.body.appendChild(container);

    const closeDialog = (result: boolean) => {
      render(null, container);
      document.body.removeChild(container);
      resolve(result);
    };

    const props: ConfirmDialogServiceOption = {
      ...options,
      onConfirm: () => closeDialog(true),
      onCancel: () => closeDialog(false),
    };

    render(h(ConfirmDialog, props), container);
  });
}
