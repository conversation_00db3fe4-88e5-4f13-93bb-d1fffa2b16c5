import type { AnyObject } from '@/types';
import { TableV2SortOrder, type SortBy } from 'element-plus';
import { ref, type Ref, toValue, watchEffect } from 'vue';

export interface FilterOption<T> {
  /** 搜索字段 */
  fields?: (keyof T)[];
  /** 自定义过滤函数 */
  customFilter?: (item: T) => boolean;
  /** 是否有选择列 */
  select?: boolean;
  /** 唯一标识字段 */
  identity: keyof T;
  /** 排序状态 */
  sortState: Ref<SortBy>;
}

/**
 * 根据查询字符串过滤数据的组合式函数
 * @template T 数据项类型
 * @param {Ref<T[]>} data 原始数据数组的响应式引用
 * @param {object} [options] 可选配置对象
 */
export const useFilteredData = <T extends AnyObject>(
  getData: () => T[],
  options: FilterOption<T>,
) => {
  const { fields = [], customFilter, identity, select, sortState } = options;
  const query = ref('');
  // 已选择的行
  const selectedRowsMap = ref<Record<string | number, boolean>>({});
  const filteredData: Ref<T[]> = ref([]);

  watchEffect(() => {
    const data = toValue(getData);
    /**
     * 未指定关键字 & 没有指定自定义搜索函数 || 数据为空
     */
    if ((!query.value && !customFilter) || data.length == 0) {
      filteredData.value = data;
    } else {
      /** 获取搜索字段范围*/
      if (fields.length == 0) {
        fields.push(...Object.keys(data[0]));
      }

      const has_keywords = typeof query.value === 'string' && !!query.value;
      const lowered = has_keywords ? query.value.toLowerCase().trim() : '';
      const has_custom_filter = typeof customFilter === 'function';
      const matches = data.filter(item => {
        let is_keywords_match = true;
        let is_filter_match = true;

        if (has_keywords) {
          const target_values = fields.map(field => item[field]?.toString().toLowerCase());
          is_keywords_match = target_values.some(value => value && value.includes(lowered));
        }

        if (is_keywords_match && has_custom_filter) {
          is_filter_match = customFilter(item);
        }

        return is_keywords_match && is_filter_match;
      });

      filteredData.value = matches;
    }
    // 如果有选择列，则行数据需要添加一个checked字段
    if (select) {
      filteredData.value.forEach(row => {
        const key = row[identity] as string | number;
        if (selectedRowsMap.value[key] === undefined) {
          selectedRowsMap.value[key] = false;
        }
        (row as any).checked = selectedRowsMap.value[key];
      });
    }
    // 排序
    if (sortState.value.key) {
      const key = sortState.value.key as keyof T;
      const order = sortState.value.order;

      filteredData.value.sort((a, b) => {
        const aValue = a[key];
        const bValue = b[key];

        // 处理 null 和 undefined
        if (aValue === null || aValue === undefined) return 1;
        if (bValue === null || bValue === undefined) return -1;
        if (aValue === bValue) return 0;

        // 根据值的类型进行比较
        if (typeof aValue === 'number' && typeof bValue === 'number') {
          return order === TableV2SortOrder.ASC ? aValue - bValue : bValue - aValue;
        } else {
          // 字符串比较，支持中英文混合排序
          const result = String(aValue).localeCompare(String(bValue), 'zh-CN');
          return order === TableV2SortOrder.ASC ? result : -result;
        }
      });
    }
  });

  return {
    query,
    filteredData,
    selectedRowsMap,
  };
};
