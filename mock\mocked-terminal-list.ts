function getMockData() {
  const data: any[] = [];

  for (let i = 0; i < 100; i++) {
    data.push({
      id: i + 1,
      terminalName: `股票终端 ${i + 1}`,
      pwd: `password${i + 1}`,
      description: `Description for Terminal ${i + 1}`,
      status: i % 2 === 0 ? 1 : 0,
      interfaceType: Math.floor(Math.random() * 2) + 1,
      createTime: Date.now() - Math.floor(Math.random() * 1000000000),
      updateTime: Date.now() - Math.floor(Math.random() * 100000000),
    });
  }

  return data;
}

// Example usage
const data = getMockData();
export default data;
