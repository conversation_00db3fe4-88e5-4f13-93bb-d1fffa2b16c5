<script setup lang="tsx">
import VirtualizedTable from '../common/VirtualizedTable.vue';
import { onMounted, shallowRef, useTemplateRef } from 'vue';
import { TableV2SortOrder } from 'element-plus';
import type { ColumnDefinition, RowAction } from '@/types';
import { AccountService } from '@/api';
import { Formatter } from '@/script';
import type { MomAccount } from '../../../../xtrade-sdk/dist';

// 基础列定义
const columns: ColumnDefinition<MomAccount> = [
  { key: 'id', title: '账号ID', width: 100, sortable: true },
  { key: 'accountType', title: '账号类型', width: 100, sortable: true }, // （7真实，8虚拟子账号）
  { key: 'parentAccountId', title: '父账号ID', width: 100, sortable: true }, // （虚拟账号）
  { key: 'productId', title: '产品ID', width: 100, sortable: true },
  { key: 'bkId', title: '经纪商表ID', width: 100, sortable: true },
  { key: 'brokerId', title: '经纪商编码', width: 100, sortable: true },
  { key: 'brokerName', title: '经纪商名称', width: 100, sortable: true },
  { key: 'financeAccount', title: '资金账号', width: 100, sortable: true },
  { key: 'pwd', title: '密码', width: 100, sortable: true },
  { key: 'accountName', title: '账号名称', width: 100, sortable: true },
  { key: 'branchId', title: '营业部编码', width: 100, sortable: true },
  { key: 'assetType', title: '资产类型', width: 100, sortable: true }, // （1股票/2期货/3期权/4股票信用）
  { key: 'status', title: '状态', width: 100, sortable: true }, // （2临时禁用，1启用，0禁用）
  { key: 'extInfo', title: '扩展字段', width: 100, sortable: true }, // （JSON格式）
  {
    key: 'disableTime',
    title: '禁用时间',
    width: 150,
    sortable: true,
    cellRenderer: formatDateTime,
  },
  { key: 'authCode', title: '授权码（期货需要）', width: 100, sortable: true },
  { key: 'appId', title: '应用ID', width: 100, sortable: true },
  { key: 'orgId', title: '机构ID', width: 100, sortable: true },
  {
    key: 'createTime',
    title: '创建时间',
    width: 150,
    sortable: true,
    cellRenderer: formatDateTime,
  },
  {
    key: 'updateTime',
    title: '更新时间',
    width: 150,
    sortable: true,
    cellRenderer: formatDateTime,
  },
  { key: 'createUserId', title: '创建用户ID', width: 100, sortable: true },
  { key: 'createUserName', title: '创建用户名', width: 100, sortable: true },
  { key: 'deleteFlag', title: '删除标识', width: 100, sortable: true },
  { key: 'availableCheck', title: '可用资金检查', width: 100, sortable: true },
  { key: 'positionCheck', title: '持仓检查', width: 100, sortable: true },
  { key: 'autoInOutMoneyRecord', title: '自动记录出入金', width: 100, sortable: true },
  { key: 'overlapBalance', title: '收盘资金覆盖', width: 100, sortable: true },
  { key: 'riskCheck', title: '风控检查', width: 100, sortable: true },
  { key: 'customerId', title: '客户号', width: 100, sortable: true },
];

// 行操作
const rowActions: RowAction<MomAccount>[] = [
  {
    label: '配置',
    icon: 'setting',
    onClick: row => {
      editRow(row);
    },
  },
  {
    label: '删除',
    icon: 'remove',
    onClick: row => {
      deleteRow(row);
    },
  },
];

function editRow(row: MomAccount) {
  console.log('edit', row);
}

function deleteRow(row: MomAccount) {
  console.log('delete', row);
}

const records = shallowRef<MomAccount[]>([]);
const tableRef = useTemplateRef('tableRef');

function formatDateTime(params: any) {
  return <span>{Formatter.formatDateTime(params.cellData)}</span>;
}

async function request() {
  records.value = (await AccountService.getAccounts()) || [];
}

onMounted(() => {
  request();
});
</script>

<template>
  <VirtualizedTable
    ref="tableRef"
    :sort="{ key: 'createTime', order: TableV2SortOrder.DESC }"
    :columns="columns"
    :data="records"
    :row-actions="rowActions"
    :row-action-width="170"
    select
    fixed
  >
    <template #actions>
      <div class="actions" flex aic>
        <el-button link size="small" class="typical-text-button">
          <i class="iconfont icon-setting"></i>
          <span>列配置</span>
        </el-button>
        <el-button link size="small" class="typical-text-button">
          <i class="iconfont icon-download"></i>
          <span>下载</span>
        </el-button>
        <el-button type="primary">
          <i class="iconfont icon-add-new" mr-5></i>
          <span>新建账号</span>
        </el-button>
      </div>
    </template>
  </VirtualizedTable>
</template>

<style scoped></style>
