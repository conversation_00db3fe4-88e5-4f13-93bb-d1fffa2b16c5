<script setup lang="ts">
import { type Menu } from '@/types';
import type { TabPaneName } from 'element-plus';
import { computed } from 'vue';

const { tabs, activeMenu } = defineProps<{
  tabs: Menu[];
  activeMenu: Menu;
}>();

const emit = defineEmits<{
  clickMenu: [item: Menu];
  closeTab: [item: Menu];
}>();

const activeName = computed({
  get() {
    return activeMenu.component;
  },
  set(value) {
    emit('clickMenu', tabs.find(tab => tab.component === value)!);
  },
});

const handleCloseTab = (tabName: TabPaneName) => {
  emit('closeTab', tabs.find(tab => tab.component === tabName)!);
};
</script>

<template>
  <div class="home-tabs" w-full>
    <el-tabs
      type="card"
      :editable="tabs.length > 1"
      v-model="activeName"
      @tab-remove="handleCloseTab"
    >
      <el-tab-pane
        v-for="tab in tabs"
        :key="tab.name"
        :label="tab.name"
        :name="tab.component"
      ></el-tab-pane>
    </el-tabs>
  </div>
</template>

<style scoped>
.home-tabs {
  .el-tabs {
    background-color: unset !important;
    --el-transition-duration: 0.1s;
  }

  :deep() {
    .el-tabs__nav-next,
    .el-tabs__nav-prev {
      line-height: 32px;
    }
    .el-tabs__nav {
      gap: 8px;
      background-color: var(--g-bg);
      border: unset;

      .el-tabs__item {
        padding: 9px 10px 9px 20px !important;
        background-color: var(--g-panel-bg2);
        border-radius: 4px !important;
        font-size: 14px !important;
        font-weight: 400 !important;
        color: var(--g-panel-text) !important;

        &:hover {
          background-color: var(--g-bg-hover-4) !important;
          color: var(--g-text-color-2) !important;
        }

        &.is-active {
          background-color: var(--g-active) !important;
          color: var(--g-text-color-2) !important;
        }
      }
    }
  }
}
</style>
