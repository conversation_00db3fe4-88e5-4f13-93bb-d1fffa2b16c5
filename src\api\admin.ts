import { Repos, type FormRole, type MomRole } from '../../../xtrade-sdk/dist';
import type { RoleMenuPermission } from '@/types';

const adminRepo = new Repos.AdminRepo();

class AdminService {
  static async getUsers() {
    const { errorCode, errorMsg, data } = await adminRepo.QueryUsers();
    if (errorCode == 0) {
      return data!;
    } else {
      console.error(errorMsg);
      return [];
    }
  }

  static async getOrgs() {
    const { errorCode, errorMsg, data } = await adminRepo.QueryOrgs();
    if (errorCode == 0) {
      return data!;
    } else {
      console.error(errorMsg);
      return [];
    }
  }

  static async getRoles() {
    const { errorCode, errorMsg, data } = await adminRepo.QueryRoles();
    if (errorCode == 0) {
      return data!;
    } else {
      console.error(errorMsg);
      return [];
    }
  }

  static async createRole(role: FormRole) {
    return adminRepo.CreateRole(role);
  }

  static async updateRole(role: MomRole) {
    return adminRepo.UpdateRole(role);
  }

  static async deleteRole(role_id: number) {
    return adminRepo.DeleteRole(role_id);
  }

  static async getMenuTree() {
    const { errorCode, errorMsg, data } = await adminRepo.QueryMenuTree();
    if (errorCode == 0) {
      return data!;
    } else {
      console.error(errorMsg);
      return [];
    }
  }

  static async getRoleMenuTree(roleId: number) {
    const { errorCode, errorMsg, data } = await adminRepo.QueryRoleMenu(roleId);
    if (errorCode == 0) {
      return data!;
    } else {
      console.error(errorMsg);
      return [];
    }
  }

  static async saveRoleMenuPermissions(role_id: number, data: RoleMenuPermission[]) {
    return adminRepo.SaveRoleMenuPermissions(role_id, data);
  }

  static async getBrokers() {
    const { errorCode, errorMsg, data } = await adminRepo.QueryBrokers();
    if (errorCode == 0) {
      return data!;
    } else {
      console.error(errorMsg);
      return [];
    }
  }

  static async getTerminals() {
    const { errorCode, errorMsg, data } = await adminRepo.QueryTerminals();
    if (errorCode == 0) {
      return data!;
    } else {
      console.error(errorMsg);
      return [];
    }
  }
}

export default AdminService;
