function getMockData() {
  const data: any[] = [];

  for (let i = 0; i < 100; i++) {
    data.push({
      id: i + 1,
      accountType: i % 2 === 0 ? 7 : 8,
      parentAccountId: i % 2 === 0 ? null : Math.floor(Math.random() * 50) + 1,
      productId: Math.floor(Math.random() * 10) + 1,
      bkId: Math.floor(Math.random() * 10) + 1,
      brokerId: `经纪商${Math.floor(Math.random() * 5) + 1}`,
      brokerName: `经纪商 ${Math.floor(Math.random() * 5) + 1}`,
      financeAccount: `FINANCE_ACCOUNT${i + 1}`,
      pwd: `password${i + 1}`,
      accountName: `账号 Name ${i + 1}`,
      branchId: `BRANCH${Math.floor(Math.random() * 5) + 1}`,
      assetType: Math.floor(Math.random() * 4) + 1,
      status: i % 3 === 0 ? 0 : i % 3 === 1 ? 1 : 2,
      extInfo: JSON.stringify({ key: `value${i}` }),
      disableTime: Date.now() - Math.floor(Math.random() * *********0),
      authCode: `AUTH_CODE${i + 1}`,
      appId: `APP_ID${i + 1}`,
      orgId: Math.floor(Math.random() * 10) + 1,
      createTime: Date.now() - Math.floor(Math.random() * *********0),
      updateTime: Date.now() - Math.floor(Math.random() * *********),
      createUserId: Math.floor(Math.random() * 10) + 1,
      createUserName: `User ${Math.floor(Math.random() * 10) + 1}`,
      deleteFlag: false,
      availableCheck: i % 2 === 0,
      positionCheck: i % 2 === 0,
      autoInOutMoneyRecord: i % 2 === 0,
      overlapBalance: i % 2 === 0,
      riskCheck: i % 2 === 0,
      customerId: `CUSTOMER_ID${i + 1}`,
    });
  }

  return data;
}

// Example usage
const data = getMockData();
export default data;
