import type { MetaDataItem } from '@/types';
import { HttpAssist } from '../../../xtrade-sdk/dist';
import type { RiskIndicator, RiskRule, RiskTemplate } from '../../../xtrade-sdk/dist';

class RiskControlService {
  static async getTemplates() {
    try {
      const resp = await HttpAssist.Get<RiskTemplate[]>('/risk-control/template/list');
      const { errorCode, errorMsg, data } = resp;
      if (errorCode == 0) {
        return data;
      } else {
        console.error(errorMsg);
        return [];
      }
    } catch (error) {
      console.error('获取风控模板失败:', error);
      return [];
    }
  }

  static async getIndicators() {
    try {
      const resp = await HttpAssist.Get<RiskIndicator[]>('/risk-control/indicator/list');
      const { errorCode, errorMsg, data } = resp;
      if (errorCode == 0) {
        return data;
      } else {
        console.error(errorMsg);
        return [];
      }
    } catch (error) {
      console.error('获取风控指标失败:', error);
      return [];
    }
  }

  static async getRules() {
    try {
      const resp = await HttpAssist.Get<RiskRule[]>('/risk-control/rule/list');
      const { errorCode, errorMsg, data } = resp;
      if (errorCode == 0) {
        return data;
      } else {
        console.error(errorMsg);
        return [];
      }
    } catch (error) {
      console.error('获取风控项失败:', error);
      return [];
    }
  }

  static async deleteRules(ids: number[]) {
    return await HttpAssist.Delete('/risk-control/rule', { ids });
  }

  static async getAssetsByCategory(category: number) {
    try {
      const resp = await HttpAssist.Get<MetaDataItem[]>('/market/asset', { category });
      const { errorCode, errorMsg, data } = resp;
      if (errorCode == 0) {
        return data;
      } else {
        console.error(errorMsg);
        return [];
      }
    } catch (error) {
      console.error('获取资产失败:', error);
      return [];
    }
  }
}

export default RiskControlService;
