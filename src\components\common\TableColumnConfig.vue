<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { ElCheckbox, ElCollapse, ElCollapseItem } from 'element-plus';
import { Utils } from '@/script';
import { Search } from '@element-plus/icons-vue';
import { type TableColumnBasic, type TableColumnConfigParamMeta } from '@/types';

const { visible, config } = defineProps<{
  visible: boolean;
  config: TableColumnConfigParamMeta;
}>();

const dialogVisible = ref(true);
const menuGroups = computed(() => {
  const { groups, columns } = config;
  return Array.isArray(groups) && groups.length > 0
    ? groups
    : Array.isArray(columns) && columns.length > 0
      ? [{ category: '所有列', columns }]
      : [];
});

const totalColumns = computed(() => {
  return menuGroups.value.map(x => x.columns).flat().length;
});

const title = computed(() => {
  return `表格列配置 - ${config.name}`;
});

const query = ref('');
const collapses = ref<string[]>([]);
const checkes = ref<string[]>([]);

watch(
  () => visible,
  value => {
    dialogVisible.value = !!value;
  },
  { immediate: true },
);

watch(
  () => menuGroups.value,
  () => {
    resetCallapses();
  },
  { immediate: true },
);

watch(
  () => config.selected,
  value => {
    checkes.value = value;
  },
  { immediate: true },
);

const emitter = defineEmits<{
  changed: [selcteds: string[]];
  canceled: [];
}>();

function resetCallapses() {
  setTimeout(() => {
    collapses.value = menuGroups.value.map(x => x.category);
  }, 50);
}

function toggleSelection(value: string) {
  Utils.remove(checkes.value, x => x == value);
}

function isMatched(col: TableColumnBasic) {
  const kw = (query.value || '').toLowerCase();
  return !kw || col.title.toLowerCase().includes(kw) || col.datakey.toLowerCase().includes(kw);
}

function confirm() {
  let results = checkes.value.slice(0);
  if (results.length == 0 || results.length == totalColumns.value) {
    results = [];
  }

  checkes.value = [];
  emitter('changed', results);
}

function cancel() {
  checkes.value = [];
  emitter('canceled');
}
</script>

<template>
  <div class="table-column-config">
    <el-dialog :title="title" width="300px" v-model="dialogVisible" center>
      <div mb-10>
        <el-input
          :prefix-icon="Search"
          class="typical-search-box-2 w-270!"
          placeholder="搜索"
          v-model.trim="query"
          @before-close="dialogVisible = false"
          clearable
        ></el-input>
      </div>
      <div max-h-400 overflow-y-auto>
        <el-collapse v-model="collapses">
          <el-collapse-item
            v-for="(group, group_idx) in menuGroups"
            :key="group_idx"
            :title="group.category"
            :name="group.category"
          >
            <div v-for="(col, col_index) in group.columns" :key="col_index" v-show="isMatched(col)">
              <el-checkbox
                :label="col.title"
                :checked="checkes.indexOf(col.datakey) > 0"
                @change="toggleSelection(col.datakey)"
              />
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
      <slot name="footer">
        <div class="dialog-footer" pt-15 flex jcc gap-20>
          <el-button type="primary" @click="confirm">确定</el-button>
          <el-button @click="cancel">取消</el-button>
        </div>
      </slot>
    </el-dialog>
  </div>
</template>

<style scoped>
.table-column-config {
  width: 100%;
  max-width: 300px;
}
</style>
