import type { RiskRule } from '../../xtrade-sdk/dist';
import indicators from './mocked-risk-indicator-list';

const data: RiskRule[] = [
  {
    id: 1,
    templateId: 101,
    ruleName: '',
    indicatorId: 0,
    configuration: {
      maxOrderSize: 1000000,
      threshold: 0.95,
    },
    beginTime: 90000,
    endTime: 150000,
    beginDay: 20231001,
    endDay: 20231231,
    checkObject: 1,
    checkTime: 1,
    checkInterval: 60,
    isActive: true,
    createUserId: 1001,
    orgId: 201,
  },
  {
    id: 2,
    templateId: 102,
    ruleName: '',
    indicatorId: 0,
    configuration: {
      whitelistExpiry: '2024-12-31',
      priorityLevel: 1,
    },
    beginTime: 80000,
    endTime: 170000,
    beginDay: 20231101,
    endDay: 20241231,
    checkObject: 2,
    checkTime: 2,
    checkInterval: 300,
    isActive: true,
    createUserId: 1002,
    orgId: 202,
  },
  {
    id: 3,
    templateId: 103,
    ruleName: '',
    indicatorId: 0,
    configuration: {
      limitAmount: 5000000,
      currency: 'CNY',
    },
    beginTime: 93000,
    endTime: 143000,
    beginDay: 20230901,
    endDay: 20240831,
    checkObject: 3,
    checkTime: 1,
    checkInterval: 15,
    isActive: false,
    createUserId: 1003,
    orgId: 203,
  },
  {
    id: 4,
    templateId: 104,
    ruleName: '',
    indicatorId: 0,
    configuration: {
      dailyLimit: 100,
      weeklyLimit: 500,
    },
    beginTime: 91500,
    endTime: 151500,
    beginDay: 20231015,
    endDay: 20241014,
    checkObject: 1,
    checkTime: 2,
    checkInterval: 60,
    isActive: true,
    createUserId: 1004,
    orgId: 204,
  },
  {
    id: 5,
    templateId: 105,
    ruleName: '',
    indicatorId: 0,
    configuration: {
      transactionLimit: 20000000,
      alertThreshold: 0.8,
    },
    beginTime: 90000,
    endTime: 153000,
    beginDay: 20231201,
    endDay: 20241130,
    checkObject: 2,
    checkTime: 1,
    checkInterval: 30,
    isActive: true,
    createUserId: 1005,
    orgId: 205,
  },
  {
    id: 6,
    templateId: 106,
    ruleName: '',
    indicatorId: 0,
    configuration: {
      netBuyLimit: 50000000,
      period: 'DAILY',
    },
    beginTime: 90500,
    endTime: 152500,
    beginDay: 20240101,
    endDay: 20241231,
    checkObject: 3,
    checkTime: 2,
    checkInterval: 120,
    isActive: false,
    createUserId: 1006,
    orgId: 206,
  },
  {
    id: 7,
    templateId: 107,
    ruleName: '',
    indicatorId: 0,
    configuration: {
      maxTransactions: 100,
      timeWindow: 'MINUTE',
    },
    beginTime: 90000,
    endTime: 150000,
    beginDay: 20231101,
    endDay: 20241031,
    checkObject: 1,
    checkTime: 1,
    checkInterval: 10,
    isActive: true,
    createUserId: 1007,
    orgId: 207,
  },
  {
    id: 8,
    templateId: 108,
    ruleName: '',
    indicatorId: 0,
    configuration: {
      cancelRatioLimit: 0.3,
      alertLevel: 'WARNING',
    },
    beginTime: 91000,
    endTime: 145000,
    beginDay: 20231010,
    endDay: 20241009,
    checkObject: 2,
    checkTime: 2,
    checkInterval: 45,
    isActive: true,
    createUserId: 1008,
    orgId: 208,
  },
  {
    id: 9,
    templateId: 109,
    ruleName: '',
    indicatorId: 0,
    configuration: {
      feeRatioThreshold: 0.15,
      actionType: 'NOTIFY',
    },
    beginTime: 92000,
    endTime: 151000,
    beginDay: 20231215,
    endDay: 20241214,
    checkObject: 3,
    checkTime: 1,
    checkInterval: 20,
    isActive: true,
    createUserId: 1009,
    orgId: 209,
  },
  {
    id: 10,
    templateId: 110,
    ruleName: '',
    indicatorId: 0,
    configuration: {
      maxPortfolioPercentage: 0.25,
      rebalanceFrequency: 'DAILY',
    },
    beginTime: 90000,
    endTime: 153000,
    beginDay: 20240301,
    endDay: 20250228,
    checkObject: 1,
    checkTime: 2,
    checkInterval: 300,
    isActive: true,
    createUserId: 1010,
    orgId: 210,
  },
];

data.forEach(item => {
  const max = indicators.length - 1;
  const pos = Math.round((Math.random() * 999999999) % max);
  const which = indicators[pos];
  item.ruleName = which.indicatorName;
  item.indicatorId = which.id;
});

export default data;
