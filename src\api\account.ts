import { Repos } from '../../../xtrade-sdk';

const govRepo = new Repos.GovernanceRepo();

class AccountService {
  static async getAccounts() {
    try {
      const resp = await govRepo.QueryAccounts();
      const { errorCode, errorMsg, data } = resp;
      if (errorCode == 0) {
        return data;
      } else {
        console.error(errorMsg);
        return [];
      }
    } catch (error) {
      console.error('获取账号失败:', error);
      return [];
    }
  }
}

export default AccountService;
