<script setup lang="tsx">
import VirtualizedTable from '../common/VirtualizedTable.vue';
import { onMounted, shallowRef, useTemplateRef } from 'vue';
import { TableV2SortOrder } from 'element-plus';
import type { ColumnDefinition, RowAction } from '@/types';
import { AdminService } from '@/api';
import { Formatter } from '@/script';
import type { MomBroker } from '../../../../xtrade-sdk/dist';

// 基础列定义
const columns: ColumnDefinition<MomBroker> = [
  { key: 'id', title: '经纪商ID', width: 100, sortable: true },
  { key: 'brokerId', title: '经纪商代码', width: 100, sortable: true },
  { key: 'brokerName', title: '经纪商名称', width: 100, sortable: true },
  { key: 'brokerType', title: '经纪商类型', width: 100, sortable: true }, //（股票/期货/股票期货）
  { key: 'servers', title: '服务器地址列表', width: 200, sortable: true }, // （JSON格式）
  {
    key: 'createTime',
    title: '创建时间',
    width: 150,
    sortable: true,
    cellRenderer: formatDateTime,
  },
  {
    key: 'updateTime',
    title: '更新时间',
    width: 150,
    sortable: true,
    cellRenderer: formatDateTime,
  },
  { key: 'description', title: '备注', width: 100, sortable: true },
];

// 行操作
const rowActions: RowAction<MomBroker>[] = [
  {
    label: '配置',
    icon: 'setting',
    onClick: row => {
      editRow(row);
    },
  },
  {
    label: '删除',
    icon: 'remove',
    onClick: row => {
      deleteRow(row);
    },
  },
];

function editRow(row: MomBroker) {
  console.log('edit', row);
}

function deleteRow(row: MomBroker) {
  console.log('delete', row);
}

const records = shallowRef<MomBroker[]>([]);
const tableRef = useTemplateRef('tableRef');

function formatDateTime(params: any) {
  return <span>{Formatter.formatDateTime(params.cellData)}</span>;
}

async function request() {
  records.value = (await AdminService.getBrokers()) || [];
}

onMounted(() => {
  request();
});
</script>

<template>
  <VirtualizedTable
    ref="tableRef"
    :sort="{ key: 'createTime', order: TableV2SortOrder.DESC }"
    :columns="columns"
    :data="records"
    :row-actions="rowActions"
    :row-action-width="170"
    select
    fixed
  >
    <template #actions>
      <div class="actions" flex aic>
        <el-button link size="small" class="typical-text-button">
          <i class="iconfont icon-setting"></i>
          <span>列配置</span>
        </el-button>
        <el-button link size="small" class="typical-text-button">
          <i class="iconfont icon-download"></i>
          <span>下载</span>
        </el-button>
        <el-button type="primary">
          <i class="iconfont icon-add-new" mr-5></i>
          <span>新建经纪商</span>
        </el-button>
      </div>
    </template>
  </VirtualizedTable>
</template>

<style scoped></style>
