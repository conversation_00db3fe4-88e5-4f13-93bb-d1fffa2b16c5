function getMockData() {
  const data: any[] = [];

  // 添加测试角色
  data.push({
    id: 175428838368449,
    roleName: '测试角色',
    orgId: 1,
    orgName: 'Test Organization',
    activeFlag: true,
    description: '用于测试菜单权限的角色',
    createTime: Date.now() - 1000000000,
    updateTime: Date.now() - 100000000,
  });

  for (let i = 0; i < 99; i++) {
    data.push({
      id: i + 1,
      roleName: `角色 ${i + 1}`,
      orgId: Math.floor(Math.random() * 10) + 1,
      orgName: `Organization ${Math.floor(Math.random() * 10) + 1}`,
      activeFlag: i % 2 === 0,
      description: `Description for Role ${i + 1}`,
      createTime: Date.now() - Math.floor(Math.random() * 1000000000),
      updateTime: Date.now() - Math.floor(Math.random() * 100000000),
    });
  }

  return data;
}

// Example usage
export default getMockData;
