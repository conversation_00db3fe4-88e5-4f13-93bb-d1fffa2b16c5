import type { MenuOperationDefinition } from '@/types';

/**
 * 页面操作
 */
const PageOperations: MenuOperationDefinition[] = [
  {
    menuId: 200,
    name: '产品管理',
    key: 'product_management',
    operations: [
      { name: '创建', key: 'create', method: 'GET', url: '/fund' },
      { name: '编辑', key: 'edit', method: 'PUT', url: '/fund' },
      { name: '删除', key: 'delete', method: 'DELETE', url: '/fund' },
      { name: '查看报告', key: 'view_report', method: null },
      {
        name: '配置报告模版',
        key: 'configure_report_template',
        method: 'POST',
        url: '/template/bind',
      },
      { name: '配置风控', key: 'configure_risk_control', method: null },
      { name: '设置为清盘', key: 'set_liquidation', method: 'PUT', url: '/fund' },
      { name: '启用风控', key: 'enable_risk_control', method: 'PUT', url: '/fund' },
      { name: '分享给用户', key: 'share_with_user', method: 'POST', url: '/fund/share' },
      { name: '绑定账号', key: 'bind_account', method: 'POST', url: '/account/bind-fund' },
      { name: '配置估值方式', key: 'configure_valuation_method', method: 'PUT', url: '/fund' },
      { name: '交易行为设置', key: 'trading_behavior_settings', method: null },
    ],
  },
  {
    menuId: 1900,
    name: '产品概览',
    key: 'product_overview',
    operations: [],
  },
  {
    menuId: -1,
    name: '产品净值',
    key: 'product_net_value',
    operations: [],
  },
  {
    menuId: -1,
    name: '今日订单',
    key: 'today_orders',
    operations: [
      { name: '工具栏撤销全部', key: 'toolbar_cancel_all', method: 'TCP', functionCode: 10002 },
      {
        name: '工具栏撤销全部买单',
        key: 'toolbar_cancel_all_bids',
        method: 'TCP',
        functionCode: 10002,
      },
      {
        name: '工具栏撤销全部卖单',
        key: 'toolbar_cancel_all_asks',
        method: 'TCP',
        functionCode: 10002,
      },
      { name: '工具栏撤勾选', key: 'toolbar_cancel_selected', method: 'TCP', functionCode: 10002 },
      { name: '撤单条', key: 'cancel_order', method: 'TCP', functionCode: 10002 },
      { name: '添加单条', key: 'add_order', method: 'POST', url: '/order/add' },
      { name: '编辑单条', key: 'edit_order', method: 'POST', url: '/order/update' },
      { name: '删除单条', key: 'delete_order', method: 'DELETE', url: '/order/delete' },
    ],
  },
  {
    menuId: -1,
    name: '今日持仓',
    key: 'today_positions',
    operations: [
      { name: '添加调仓记录', key: 'add_position_adjustment_record', method: null },
      { name: '添加单条', key: 'add_position', method: 'POST', url: '/position/add' },
      { name: '编辑单条', key: 'edit_position', method: 'POST', url: '/position/update' },
      { name: '删除单条', key: 'delete_position', method: 'DELETE', url: '/position/delete' },
    ],
  },
  {
    menuId: -1,
    name: '今日成交',
    key: 'today_trades',
    operations: [
      { name: '添加单条', key: 'add_trade', method: 'POST', url: '/trade/add' },
      { name: '编辑单条', key: 'edit_trade', method: 'POST', url: '/trade/update' },
      { name: '删除单条', key: 'delete_trade', method: 'DELETE', url: '/trade/delete' },
    ],
  },
  {
    menuId: -1,
    name: '历史订单',
    key: 'historical_orders',
    operations: [],
  },
  {
    menuId: -1,
    name: '历史持仓',
    key: 'historical_positions',
    operations: [],
  },
  {
    menuId: -1,
    name: '历史成交',
    key: 'historical_trades',
    operations: [],
  },
  {
    menuId: -1,
    name: '历史权益',
    key: 'historical_equity',
    operations: [],
  },
  {
    menuId: 300,
    name: '账号管理',
    key: 'account_management',
    operations: [
      { name: '创建', key: 'create', method: 'POST', url: '/account' },
      { name: '编辑', key: 'edit', method: 'PUT', url: '/account' },
      { name: '删除', key: 'delete', method: 'DELETE', url: '/account' },
      { name: '设置终端', key: 'set_terminal', method: 'POST', url: '/account/bind-terminal' },
      { name: '绑定流程', key: 'bind_process', method: 'PUT', url: '/workflow/bind' },
      {
        name: '连接终端',
        key: 'connect_disconnect_switch',
        method: 'GET',
        url: '/account/connection_test',
      },
      {
        name: '断开终端',
        key: 'disconnect_disconnect_switch',
        method: 'PUT',
        url: '/account/logout',
      },
      { name: '启用禁用', key: 'enable_disable_switch', method: 'PUT', url: '/account' },
      {
        name: '盘前风控',
        key: 'pre_market_risk_control_switch',
        method: 'PUT',
        url: '/account/updatePreRiskControl',
      },
      { name: '快速审核', key: 'quick_review_switch', method: 'PUT', url: '/account/autoApprove' },
      { name: '费用设置', key: 'fee_settings', method: 'GET', url: '/account/fee' },
      { name: '密码维护', key: 'password_maintenance', method: 'PUT', url: '/account' },
      { name: '初始化', key: 'initialize', method: 'POST', url: '/account/init' },
      { name: '比对', key: 'compare', method: 'POST', url: '/account/overlap' },
      { name: '覆盖', key: 'override', method: 'POST', url: '/account/overlap' },
      { name: '权益维护', key: 'equity_maintenance', method: 'POST', url: '/account/prebalance' },
      {
        name: '资金覆盖',
        key: 'available_override',
        method: 'POST',
        url: '/account/overlap/detail',
      },
      {
        name: '资金划转',
        key: 'available_transfer',
        method: 'POST',
        url: '/account/transFundsOther',
      },
      { name: '清算', key: 'settlement', method: 'PUT', url: '/account/settle' },
      { name: '风控设置', key: 'risk_control_settings', method: null },
      { name: '交易行为设置', key: 'trading_behavior_settings', method: null },
    ],
  },
  {
    menuId: -1,
    name: '出入金',
    key: 'fund_in_out',
    operations: [{ name: '添加', key: 'add', method: 'POST', url: '/account/cashserial' }],
  },
  {
    menuId: 303,
    name: '账号监控',
    key: 'account_monitoring',
    operations: [
      { name: '覆盖', key: 'override', method: 'POST', url: '/account/overlap' },
      { name: '成交检测', key: 'trade_detection', method: 'POST', url: '/account/check/trade' },
      { name: '委托检测', key: 'order_detection', method: 'POST', url: '/account/check/order' },
      { name: '资金检测', key: 'fund_detection', method: 'POST', url: '/account/check/balance' },
    ],
  },
  {
    menuId: 2201,
    name: '权益查询',
    key: 'equity_query',
    operations: [],
  },
  {
    menuId: 2202,
    name: '委托查询',
    key: 'order_query',
    operations: [],
  },
  {
    menuId: 2203,
    name: '持仓查询',
    key: 'position_query',
    operations: [],
  },
  {
    menuId: 2204,
    name: '成交查询',
    key: 'trade_query',
    operations: [],
  },
  {
    menuId: 2301,
    name: '普通交易',
    key: 'normal_trading',
    operations: [
      { name: '现货竞价交易', key: 'spot_auction_trading', method: null },
      { name: '信用交易', key: 'credit_trading', method: null },
      { name: '可转债交易', key: 'convertible_bond_trading', method: null },
      { name: '期货交易', key: 'futures_trading', method: null },
    ],
  },
  {
    menuId: 2302,
    name: '批量交易',
    key: 'batch_trading',
    operations: [
      { name: '现货竞价交易', key: 'spot_auction_trading', method: null },
      { name: '信用交易', key: 'credit_trading', method: null },
      { name: '可转债交易', key: 'convertible_bond_trading', method: null },
      { name: '期货交易', key: 'futures_trading', method: null },
    ],
  },
  {
    menuId: 2303,
    name: '篮子算法交易',
    key: 'basket_algorithm_trading',
    operations: [
      { name: '现货竞价交易', key: 'spot_auction_trading', method: null },
      { name: '信用交易', key: 'credit_trading', method: null },
      { name: '可转债交易', key: 'convertible_bond_trading', method: null },
      { name: '期货交易', key: 'futures_trading', method: null },
      { name: '创建篮子', key: 'create_basket', method: null },
      { name: '删除篮子', key: 'delete_basket', method: null },
      { name: '篮子调仓', key: 'basket_rebalancing', method: null },
    ],
  },
  {
    menuId: 2304,
    name: '算法交易',
    key: 'algorithm_trading',
    operations: [
      { name: '现货竞价交易', key: 'spot_auction_trading', method: null },
      { name: '信用交易', key: 'credit_trading', method: null },
      { name: '可转债交易', key: 'convertible_bond_trading', method: null },
      { name: '期货交易', key: 'futures_trading', method: null },
      { name: '创建篮子', key: 'create_basket', method: null },
      { name: '删除篮子', key: 'delete_basket', method: null },
      { name: '篮子调仓', key: 'basket_rebalancing', method: null },
    ],
  },
  {
    menuId: 2305,
    name: '文件扫单',
    key: 'file_scanning',
    operations: [],
  },
  {
    menuId: 1601,
    name: '待审核订单',
    key: 'pending_review_orders',
    operations: [
      { name: '全部通过', key: 'approve_all', method: 'TCP', functionCode: 11010 },
      { name: '通过勾选', key: 'approve_selected', method: 'TCP', functionCode: 11010 },
      { name: '全部驳回', key: 'reject_all', method: 'TCP', functionCode: 11010 },
      { name: '驳回勾选', key: 'reject_selected', method: 'TCP', functionCode: 11010 },
      { name: '通过', key: 'approve', method: 'TCP', functionCode: 11010 },
      { name: '驳回', key: 'reject', method: 'TCP', functionCode: 11010 },
    ],
  },
  {
    menuId: 1602,
    name: '已审核订单',
    key: 'reviewed_orders',
    operations: [],
  },
  {
    menuId: 1603,
    name: '已执行订单',
    key: 'executed_orders',
    operations: [],
  },
  {
    menuId: 1700,
    name: '流程管理',
    key: 'process_management',
    operations: [
      { name: '创建', key: 'create', method: 'POST', url: '/workflow' },
      { name: '编辑', key: 'edit', method: 'PUT', url: '/workflow' },
      { name: '删除', key: 'delete', method: 'DELETE', url: '/workflow' },
    ],
  },
  {
    menuId: 100,
    name: '机构管理',
    key: 'organization_management',
    operations: [
      { name: '创建', key: 'create', method: 'POST', url: '/organization' },
      { name: '编辑', key: 'edit', method: 'PUT', url: '/organization' },
      { name: '删除', key: 'delete', method: 'DELETE', url: '/organization' },
      { name: '启用禁用', key: 'enable_disable', method: 'PUT', url: '/organization' },
    ],
  },
  {
    menuId: 500,
    name: '角色管理',
    key: 'role_management',
    operations: [
      { name: '创建', key: 'create', method: 'POST', url: '/role' },
      { name: '编辑', key: 'edit', method: 'PUT', url: '/role' },
      { name: '删除', key: 'delete', method: 'DELETE', url: '/role' },
      { name: '权限配置', key: 'permission_configuration', method: null },
    ],
  },
  {
    menuId: 600,
    name: '用户管理',
    key: 'user_management',
    operations: [
      { name: '创建', key: 'create', method: 'POST', url: '/user' },
      { name: '编辑', key: 'edit', method: 'PUT', url: '/user' },
      { name: '删除', key: 'delete', method: 'DELETE', url: '/user' },
      { name: '启用禁用', key: 'enable_disable', method: 'PUT', url: '/user' },
      { name: '强制下线', key: 'force_kick_out', method: 'PUT', url: '/user/logout' },
      { name: '重置密码', key: 'reset_password', method: 'PUT', url: '/user/password' },
      { name: '查看登录日志', key: 'view_login_log', method: 'GET', url: '/log/action' },
      { name: '查看操作日志', key: 'view_operation_log', method: 'GET', url: '/log/action' },
    ],
  },
  {
    menuId: 1100,
    name: '经纪商管理',
    key: 'broker_management',
    operations: [
      { name: '创建', key: 'create', method: 'POST', url: '/broker' },
      { name: '编辑', key: 'edit', method: 'PUT', url: '/broker' },
      { name: '删除', key: 'delete', method: 'DELETE', url: '/broker' },
    ],
  },
  {
    menuId: 800,
    name: '终端管理',
    key: 'terminal_management',
    operations: [
      { name: '创建', key: 'create', method: 'POST', url: '/terminal' },
      { name: '编辑', key: 'edit', method: 'PUT', url: '/terminal' },
      { name: '删除', key: 'delete', method: 'DELETE', url: '/terminal' },
      { name: '启用禁用', key: 'enable_disable', method: 'PUT', url: '/terminal' },
    ],
  },
  {
    menuId: 1000,
    name: '风控模版管理',
    key: 'risk_template_management',
    operations: [],
  },
  {
    menuId: 9902,
    name: '风控消息',
    key: 'risk_control_message',
    operations: [],
  },
  {
    menuId: 900,
    name: '交易日',
    key: 'trading_day',
    operations: [],
  },
  {
    menuId: 1200,
    name: '运维',
    key: 'operation_maintenance',
    operations: [],
  },
];

/**
 * 菜单及权限定义唯一性检查
 */
export function uniqueCheck() {
  const map = {} as any;
  for (let i = 0; i < PageOperations.length; i++) {
    const obj1 = PageOperations[i];
    const key1 = `${obj1.name}-${obj1.key}`;
    const matched1 = map[key1];

    if (matched1 !== undefined) {
      console.error('duplicated menu', `${obj1.name}-${obj1.key}`);
      break;
    }

    for (let j = 0; j < obj1.operations.length; j++) {
      const obj2 = obj1.operations[j];
      const key2 = `${obj2.name}-${obj2.key}`;
      const matched2 = map[key2];

      if (matched2 !== undefined) {
        console.error('duplicated operation', `${obj1.name}-${obj1.key}-${obj2.name}-${obj2.key}`);
        break;
      }
    }
  }
}

/**
 * 从菜单操作权限生成SQL
 * @param defs
 */
export function makeSqls() {
  const insertions: string[] = [];
  const now = new Date().toISOString().replace('T', ' ').replace('Z', '');

  PageOperations.forEach(def => {
    def.operations.forEach(op => {
      const values = [
        0,
        `'${op.key}'`,
        `'${op.name}'`,
        op.functionCode || 0,
        op.url ? `'${op.url}'` : 'null',
        op.method ? `'${op.method}'` : 'null',
        def.menuId,
        0,
        `'${now}'`,
        `'${now}'`,
      ];
      insertions.push(`(${values.join()})`);
    });
  });

  const sql_insert =
    'insert into t_permission(userType, permissionName, permissionZhName, functionCode, url, method, menuId, defaultPermission, createTime, updateTime)';
  const sql_full = `${sql_insert} values \n${insertions.join(',\n')}`;
  console.log(sql_full);
}

function toUpperCamelCase(str: string) {
  if (!str || typeof str !== 'string') {
    return str;
  }

  return str
    .split(/_+/)
    .map(word => {
      if (word.length === 0) return '';
      return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
    })
    .join('');
}

/**
 * 从菜单操作权限生成枚举定义
 * @param defs
 */
export function makeEnums(defs: MenuOperationDefinition[]) {
  const bigMap = {} as any;

  defs.forEach(def => {
    const smallMap = {} as any;
    def.operations.forEach(op => {
      smallMap[op.name] = op.key;
    });
    bigMap[def.key] = { name: def.name, subset: smallMap };
  });

  let result = '';
  for (const key in bigMap) {
    const { name, subset } = bigMap[key];
    result += `\n\n/**
        * ${name}
        */\n`;
    result += `export enum MenuPermit${toUpperCamelCase(key)} {`;
    result += Object.entries(subset)
      .map(([name, value]) => `${name} = '${value}',`)
      .join('\n');
    result += `}`;
  }

  console.log(result);
}

export default PageOperations;
