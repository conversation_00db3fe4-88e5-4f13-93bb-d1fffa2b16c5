const data = [
  { label: '全部A股', value: 1 },
  { label: '上证主板', value: 2 },
  { label: '深圳A股', value: 3 },
  { label: '创业板', value: 4 },
  { label: '全部主板', value: 5 },
  { label: '沪股通', value: 6 },
  { label: '陆股通(含沪深)', value: 7 },
  { label: '上证A股', value: 8 },
  { label: '科创板', value: 9 },
  { label: '深圳主板', value: 10 },
  { label: '北证A股', value: 11 },
  { label: '全部双创', value: 12 },
  { label: '港股通', value: 13 },
];

function mockAssets(category: number) {
  const cloned = data.map(x => {
    return { ...x };
  });
  cloned.forEach(x => {
    x.value = Number(category) * 1000 + x.value;
  });

  return cloned;
}

export default mockAssets;
