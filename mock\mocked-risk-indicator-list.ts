import type { RiskIndicator } from '../../xtrade-sdk/dist';

const data: RiskIndicator[] = [
  { id: 0, indicatorName: '交易行为控制', indicatorCode: '100', parentIndicatorCode: null },

  { id: 1, indicatorName: '黑白名单', indicatorCode: '100-01', parentIndicatorCode: '100' },
  { id: 2, indicatorName: '黑名单', indicatorCode: '100-01-01', parentIndicatorCode: '100-01' },
  { id: 3, indicatorName: '白名单', indicatorCode: '100-01-02', parentIndicatorCode: '100-01' },

  { id: 4, indicatorName: '交易数量控制', indicatorCode: '100-02', parentIndicatorCode: '100' },
  {
    id: 5,
    indicatorName: '单笔最大委托量',
    indicatorCode: '100-02-01',
    parentIndicatorCode: '100-02',
  },
  {
    id: 6,
    indicatorName: '账户总委托数量',
    indicatorCode: '100-02-02',
    parentIndicatorCode: '100-02',
  },

  { id: 7, indicatorName: '交易额度控制', indicatorCode: '100-03', parentIndicatorCode: '100' },
  {
    id: 8,
    indicatorName: '单笔最大交易额',
    indicatorCode: '100-03-01',
    parentIndicatorCode: '100-03',
  },
  { id: 9, indicatorName: '净买入金额', indicatorCode: '100-03-02', parentIndicatorCode: '100-03' },

  { id: 10, indicatorName: '交易次数控制', indicatorCode: '100-04', parentIndicatorCode: '100' },
  {
    id: 11,
    indicatorName: '交易频率控制',
    indicatorCode: '100-04-01',
    parentIndicatorCode: '100-04',
  },
  {
    id: 12,
    indicatorName: '撤单比率限制',
    indicatorCode: '100-04-02',
    parentIndicatorCode: '100-04',
  },
  {
    id: 13,
    indicatorName: '费单比率限制',
    indicatorCode: '100-04-03',
    parentIndicatorCode: '100-04',
  },

  {
    id: 14,
    indicatorName: '同向反向对敲控制',
    indicatorCode: '100-05',
    parentIndicatorCode: '100',
  },
  { id: 15, indicatorName: '同向对敲', indicatorCode: '100-05-01', parentIndicatorCode: '100-05' },
  { id: 16, indicatorName: '反向对敲', indicatorCode: '100-05-02', parentIndicatorCode: '100-05' },
  { id: 17, indicatorName: '对敲控制', indicatorCode: '100-05-03', parentIndicatorCode: '100-05' },

  { id: 18, indicatorName: '持仓控制', indicatorCode: '200', parentIndicatorCode: null },
  { id: 19, indicatorName: '投资范围控制', indicatorCode: '200-01', parentIndicatorCode: '200' },
  { id: 20, indicatorName: '投资比例控制', indicatorCode: '200-02', parentIndicatorCode: '200' },
];

export default data;
