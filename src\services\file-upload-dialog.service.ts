import { h, render } from 'vue';
import FileUploadDialog from '../components/common/FileUploadDialog.vue';
import type {
  FileUploadDialogOption,
  FileUploadDialogServiceOption,
} from '@/types/components/basic';

/**
 * 显示文件上传对话框
 */
export function showFileUploadDialog(options: FileUploadDialogOption): Promise<boolean> {
  return new Promise(resolve => {
    const container = document.createElement('div');
    document.body.appendChild(container);

    const closeDialog = (result: boolean) => {
      render(null, container);
      document.body.removeChild(container);
      resolve(result);
    };

    const props: FileUploadDialogServiceOption = {
      ...options,
      onChoose: () => closeDialog(true),
      onUpload: () => closeDialog(true),
      onCancel: () => closeDialog(false),
    };

    render(h(FileUploadDialog, props), container);
  });
}
