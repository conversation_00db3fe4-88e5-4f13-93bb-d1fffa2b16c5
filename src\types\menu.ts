import type { MomMenu, MomPermission } from '../../../xtrade-sdk/dist';

/**
 * 菜单项接口
 */
export interface Menu {
  name: string;
  component?: string;
  children?: Menu[];
  icon?: string;
}

/**
 * 菜单分组接口
 */
export interface MenuGroup {
  name: string;
  menus: Menu[];
}

/** 菜单树接口 */
export interface SysMenuTree extends MomMenu {
  value: string; // 用于el-tree-v2的value字段
  children?: Array<MomPermission & { value: string }> | SysMenuTree[];
}

export interface TreePermission extends MomPermission {
  value: string; // 用于el-tree-v2的value字段
}

/** 角色菜单权限接口 */
export interface RoleMenuPermission {
  menuId: number;
  permissionIds: number[];
}
