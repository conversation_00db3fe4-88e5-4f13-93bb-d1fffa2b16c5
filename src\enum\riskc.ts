import { Utils } from '@/script';

/**
 * 触发风控阈值比较符
 */
export enum RiskTriggerComparer {
  '>' = 1,
  '<' = 2,
}

/**
 * 触发风控阈值比较符
 */
export const RISK_TRIGGER_COMPARERS = Utils.enumToArray(RiskTriggerComparer);

/**
 * 触发风控采取动作
 */
export enum RiskTriggerAction {
  预警 = 1,
  阻止 = 2,
}

/**
 * 触发风控采取动作
 */
export const RISK_TRIGGER_ACTIONS = Utils.enumToArray(RiskTriggerAction);

/**
 * 风控环节控制
 */
export enum RiskStepControl {
  针对指令 = 1,
  针对委托 = 2,
}

/**
 * 风控环节控制
 */
export const RISK_STEP_CONTROLS = Utils.enumToArray(RiskStepControl);
