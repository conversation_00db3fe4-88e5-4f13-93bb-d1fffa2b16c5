<script setup lang="ts">
import { type ComponentTab } from '@/types';
import { computed, defineAsyncComponent, onMounted, ref } from 'vue';
const { tabs } = defineProps<{
  tabs: ComponentTab[];
}>();

const components: Record<string, unknown> = {};

const activeName = ref('');

tabs.forEach(tab => {
  components[tab.component] = defineAsyncComponent(
    () => import(`./ComponentTabs/${tab.component}.vue`),
  );
});

const componentName = computed(() => components[activeName.value]);

const activeTab = computed(() => tabs.find(tab => tab.component === activeName.value));

onMounted(() => {
  activeName.value = tabs[0].component;
});
</script>

<template>
  <div class="component-tabs" flex="~ col">
    <el-tabs type="card" v-model="activeName">
      <el-tab-pane
        v-for="tab in tabs"
        :key="tab.component"
        :label="tab.label"
        :name="tab.component"
      />
    </el-tabs>
    <KeepAlive>
      <component
        flex-1
        min-h-1
        :is="componentName"
        v-bind="activeTab?.props || {}"
        v-on="activeTab?.events || {}"
      ></component>
    </KeepAlive>
  </div>
</template>

<style scoped>
.component-tabs {
  .el-tabs {
    background-color: var(--g-panel-bg);
  }
  :deep() {
    .el-tabs__header {
      .el-tabs__nav-wrap {
        .el-tabs__nav-scroll {
          .el-tabs__nav {
            .el-tabs__item {
              &.is-active {
                background-color: var(--g-panel-bg3);
              }
              &:hover {
                background-color: var(--g-bg-hover-1);
              }
            }
          }
        }
      }
    }
  }
}
</style>
