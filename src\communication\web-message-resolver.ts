import snappyjs from 'snappyjs';
import Utils from '../modules/utils';
import { SocketDataPackage } from '../types/data-package';
import { GetLogger } from '../global-state';
import { MessageResolver } from './message-resolver';
import { MessageDataType } from '../config/architecture';
import { ServerEvent } from '../config/server-event';

const defaultLogger = GetLogger();

/**
 * 基于WebSocket的消息数据包编码解码类
 */
export class WsMessageResolver extends MessageResolver {

    private encoder = new TextEncoder();
    private decoder = new TextDecoder();

    constructor() {
        super();
    }

    /**
     * 编码要发送到WebSocket服务器的消息
     */
    encode(message: SocketDataPackage) {

        const { fc, reqId, dataType, body } = message;
        const isBuffer = body instanceof ArrayBuffer;

        // 创建消息头（4 + 8 + 4 + 4 = 20字节）
        const headerBuffer = new ArrayBuffer(20);
        const headerView = new DataView(headerBuffer);
        
        // 写入消息头 (小端字节序)
        // fc (4 bytes)
        headerView.setInt32(0, fc, false);
        // reqId (8 bytes)
        headerView.setBigInt64(4, BigInt(reqId!), false);
        // dataType (4 bytes)
        headerView.setInt32(12, dataType!, false);

        // BODY部分预处理

        let bodyBuffer: ArrayBufferLike;

        if (isBuffer) {
            bodyBuffer = body;
        }
        else if (Utils.isNone(body)) {
            bodyBuffer = this.encoder.encode('').buffer;
        }
        else if (Utils.isJson(body) || Array.isArray(body) || typeof body == 'object') {
            bodyBuffer = this.encoder.encode(JSON.stringify(body)).buffer;
        }
        else {
            bodyBuffer = this.encoder.encode(body.toString());
        }
        
        // dataLength (4 bytes)
        headerView.setInt32(16, bodyBuffer.byteLength, false);
        // 合并消息头和消息体
        const msgBuffer = new Uint8Array(headerBuffer.byteLength + bodyBuffer.byteLength);
        msgBuffer.set(new Uint8Array(headerBuffer), 0);
        msgBuffer.set(new Uint8Array(bodyBuffer), headerBuffer.byteLength);        
        return msgBuffer;
    }

    /**
     * 解码从服务器收到的消息
     */
    decode(data: ArrayBuffer): SocketDataPackage[] {

        if (data instanceof ArrayBuffer) {

            const dataView = new DataView(data);
            // 解析消息头
            const fc = dataView.getInt32(0, false);
            const reqId = Number(dataView.getBigInt64(4, false));
            const dataType = dataView.getInt32(12, false);
            const dataLength = dataView.getInt32(16, false);
            // 提取数据部分
            const messageBody = new Uint8Array(data, 20, dataLength);
            const { defaulted, text, json, buffer, tradeServerLogon, quoteServerLogon } = MessageDataType;
            // 转换后的数据
            let body;

            if (dataType === text) {
                body = this.decoder.decode(messageBody);
            }
            else if (dataType === json || dataType === tradeServerLogon || dataType === quoteServerLogon) {

                let str = this.decoder.decode(messageBody);

                try {
                    body = JSON.parse(str);
                }
                catch(ex) {

                    defaultLogger.fatal('Unable to parse body to json', str);
                    body = str;
                }
            }
            else if (dataType === buffer || dataType === defaulted) {
                body = this.castBuffer2Json(fc, messageBody);
            }
            else {
                body = messageBody;
            }
            
            let fcEvent = ServerEvent[fc];
            return [{ fc, fcEvent, reqId, dataType, body }];
        }
        else {
            return [{ fc: 0, reqId: 0, dataType: 1, body: data }];
        }
    }

    /**
     * 将字节流数据转换为JSON对象
     */
    private castBuffer2Json(se: ServerEvent, body: Uint8Array) {

        if (se == ServerEvent.TodayOrderPush || se == ServerEvent.TodayPositionPush || se == ServerEvent.TodayTradeRecordPush) {
            body = snappyjs.uncompress(body);
        }

        const content = this.decoder.decode(body);

        try {
            return Utils.isNotNone(content) ? JSON.parse(content) : null;
        }
        catch(ex) {

            defaultLogger.error('unable to parse body to json', content);
            return body;
        }
    }
}