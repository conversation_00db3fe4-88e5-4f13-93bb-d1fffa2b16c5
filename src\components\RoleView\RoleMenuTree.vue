<script setup lang="tsx">
import { computed, onMounted, ref, shallowRef, useTemplateRef, watch } from 'vue';
import type { TreeNodeData } from 'element-plus/es/components/tree/src/tree.type';
import type { MomMenuTree, MomRole } from '../../../../xtrade-sdk/dist';
import { AdminService } from '@/api';
import type { SysMenuTree, TreePermission } from '@/types';
import { ElMessage } from 'element-plus';
import type { TreeInstance } from 'element-plus';

const { role } = defineProps<{
  role?: MomRole;
}>();

const props = {
  value: 'value',
  label: 'menuName',
  children: 'children',
};

const data = shallowRef<SysMenuTree[]>([]);
const treeRef = useTemplateRef<TreeInstance>('treeRef');
const searchText = ref('');

// 是否可以保存菜单（只有选中角色时才能保存）
const canSaveMenu = computed(() => !!role?.id);

// 所有叶子节点的id
const leafIds = computed(() => {
  const getLeafIds = (arr: SysMenuTree[]) => {
    let ids: string[] = [];
    arr.forEach(item => {
      if (item.children && item.children.length > 0) {
        ids = ids.concat(getLeafIds(item.children as SysMenuTree[]));
      } else {
        ids.push(item.value);
      }
    });
    return ids;
  };
  return getLeafIds(data.value);
});

// 监听角色变化，重新查询权限数据
watch(
  () => role?.id,
  async newRoleId => {
    if (newRoleId) {
      await loadRolePermissions(newRoleId);
    }
  },
);

onMounted(() => {
  getFullMenuAndPermissions();
});

const filterNode = (value: string, data: TreeNodeData) => {
  if (!value) return true;
  return data.menuName.includes(value);
};
const handleSearch = () => {
  treeRef.value?.filter(searchText.value);
};

// 转换菜单数据结构，添加value字段并将menListPermission改为children
const transformMenuData = (menus: MomMenuTree[]): SysMenuTree[] => {
  return menus.map(menu => {
    const transformedMenu: SysMenuTree = {
      ...menu,
      value: menu.id.toString(),
      children: [],
    };

    // 添加子菜单
    if (menu.children && menu.children.length > 0) {
      (transformedMenu.children! as SysMenuTree[]).push(...transformMenuData(menu.children));
    } else if (menu.menListPermission && menu.menListPermission.length > 0) {
      // 添加权限项作为子节点
      const permissionNodes = menu.menListPermission.map(permission => ({
        ...permission,
        menuName: permission.permissionZhName,
        value: `${menu.id}_${permission.id}`,
      }));
      (transformedMenu.children! as TreePermission[]).push(...permissionNodes);
    }

    return transformedMenu;
  });
};

const getFullMenuAndPermissions = async () => {
  const rawData = await AdminService.getMenuTree();
  data.value = transformMenuData(rawData);
};

// 加载角色菜单和权限
const loadRolePermissions = async (roleId: number) => {
  const rawData = await AdminService.getRoleMenuTree(roleId);
  const selectedKeys: string[] = [];
  const processMenu = (menu: MomMenuTree) => {
    selectedKeys.push(String(menu.id));
    // 处理权限
    if (menu.menListPermission && menu.menListPermission.length > 0) {
      menu.menListPermission.forEach(permission => {
        selectedKeys.push(`${menu.id}_${permission.id}`);
      });
    }
    // 递归处理子菜单
    if (menu.children && menu.children.length > 0) {
      menu.children.forEach(processMenu);
    }
  };
  rawData.forEach(processMenu);

  console.log('selectedKeys', selectedKeys);
  const leafSelectedKeys = selectedKeys.filter(key => leafIds.value.includes(key));
  treeRef.value?.setCheckedKeys(leafSelectedKeys);
};

// 展开所有节点
const expandAll = () => {
  const nodes = treeRef.value?.store.nodesMap;
  for (const key in nodes) {
    nodes[key].expanded = true;
  }
};

// 折叠所有节点
const collapseAll = () => {
  const nodes = treeRef.value?.store.nodesMap;
  for (const key in nodes) {
    nodes[key].expanded = false;
  }
};

const isMenu = (node: SysMenuTree | TreePermission): node is SysMenuTree => {
  return !node.value.includes('_');
};

// 保存菜单权限
const saveMenuPermissions = async () => {
  if (!role?.id) {
    ElMessage.warning('请先选择角色');
    return;
  }

  try {
    // 构建保存数据结构
    const saveData = buildSaveData();

    // 调用API保存
    const { errorCode, errorMsg } = await AdminService.saveRoleMenuPermissions(role.id, saveData);

    if (errorCode === 0) {
      ElMessage.success('保存成功');
    } else {
      ElMessage.error(errorMsg || '保存失败');
    }
  } catch (error) {
    console.error('保存菜单权限失败:', error);
    ElMessage.error('保存失败');
  }
};

// 构建保存数据结构
const buildSaveData = () => {
  const menuPermissionMap = new Map<number, number[]>();

  // 遍历选中的权限，按菜单分组
  const checkedKeys = (treeRef.value?.getCheckedKeys() as string[]) || [];
  const halfCheckedKeys = (treeRef.value?.getHalfCheckedKeys() as string[]) || [];

  checkedKeys.forEach(key => {
    if (key.includes('_')) {
      // 权限节点
      const [menuIdStr, permissionIdStr] = key.split('_');
      const menuId = parseInt(menuIdStr);
      const permissionId = parseInt(permissionIdStr);

      if (!menuPermissionMap.has(menuId)) {
        menuPermissionMap.set(menuId, []);
      }
      menuPermissionMap.get(menuId)!.push(permissionId);
    } else {
      const menuId = parseInt(key);
      menuPermissionMap.set(menuId, []);
    }
  });

  halfCheckedKeys.forEach(key => {
    const menuId = parseInt(key);
    if (!menuPermissionMap.has(menuId)) {
      menuPermissionMap.set(menuId, []);
    }
  });

  // 构建最终数据结构
  return Array.from(menuPermissionMap.entries()).map(([menuId, permissionIds]) => ({
    menuId,
    permissionIds,
  }));
};
</script>

<template>
  <div h-full flex flex-col>
    <!-- 操作按钮 -->
    <div mb-4 flex gap-2 h-60 aic jcsb px-20>
      <div flex aic gap-2>
        <el-input
          class="typical-search-box"
          v-model="searchText"
          size="small"
          placeholder="搜索菜单"
          clearable
          style="width: 200px"
          @input="handleSearch"
        />
        <el-button size="small" @click="expandAll">展开所有</el-button>
        <el-button size="small" @click="collapseAll">折叠所有</el-button>
      </div>
      <el-button type="primary" size="small" :disabled="!canSaveMenu" @click="saveMenuPermissions">
        保存菜单
      </el-button>
    </div>

    <!-- 菜单权限树 -->
    <div flex-1 overflow-auto>
      <el-tree
        class="menu-tree"
        ref="treeRef"
        :data="data"
        :props="props"
        show-checkbox
        node-key="value"
        :filter-node-method="filterNode"
      />
    </div>
  </div>
</template>

<style scoped>
.menu-tree {
  :deep() {
    .el-tree-node__children.has-button {
      display: flex;
      flex-wrap: wrap;
      padding-left: 36px;
      div {
        width: 220px;
        .el-tree-node {
          &__content {
            padding-left: 0 !important;
          }
        }
      }
    }
  }
}
</style>
