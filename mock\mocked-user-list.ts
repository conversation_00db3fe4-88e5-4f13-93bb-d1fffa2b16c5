function getMockData() {
  const data: any[] = [];

  for (let i = 0; i < 10; i++) {
    data.push({
      id: i + 1,
      userName: `用户${i + 1}`,
      fullName: `Full Name ${i + 1}`,
      password: `password${i + 1}`,
      firstLogin: i % 2 === 0,
      email: `用户${i + 1}@example.com`,
      phoneNo: `123-456-789${i}`,
      orgId: Math.floor(Math.random() * 10) + 1,
      orgName: `Organization ${Math.floor(Math.random() * 10) + 1}`,
      roleId: Math.floor(Math.random() * 10) + 1,
      roleName: `Role ${Math.floor(Math.random() * 10) + 1}`,
      status: i % 3,
      errorNum: Math.floor(Math.random() * 5),
      frozenExpireTime: Date.now() + Math.floor(Math.random() * 1000000000),
      ip: `192.168.1.${i}`,
      mac: `00:1A:2B:3C:4D:${i.toString(16).padStart(2, '0')}`,
      diskNo: Math.floor(Math.random() * 1000000),
      cpuNo: Math.floor(Math.random() * 1000000),
      createTime: Date.now() - Math.floor(Math.random() * 1000000000),
      updateTime: Date.now() - Math.floor(Math.random() * 100000000),
      deleteFlag: false,
      configuration: JSON.stringify({ key: `value${i}` }),
      multiLogin: Math.floor(Math.random() * 5) + 1,
    });
  }

  return data;
}

// Example usage
const data = getMockData();
export default data;
