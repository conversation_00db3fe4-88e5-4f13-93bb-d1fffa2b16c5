<template>
  <el-table :data="accountList" style="width: 100%">
    <el-table-column prop="account" label="资金账号"></el-table-column>
    <el-table-column prop="name" label="名称"></el-table-column>
    <el-table-column prop="type" label="类型"></el-table-column>
    <el-table-column prop="profit" label="持仓盈亏"></el-table-column>
    <el-table-column prop="todayProfit" label="当日盈亏"></el-table-column>
    <el-table-column prop="availableFunds" label="可用资金"></el-table-column>
    <el-table-column prop="totalAssets" label="总资产"></el-table-column>
    <el-table-column prop="totalLiabilities" label="总负债"></el-table-column>
    <el-table-column prop="netAssets" label="净资产"></el-table-column>
    <el-table-column prop="department" label="所属部门"></el-table-column>
    <el-table-column prop="status" label="状态"></el-table-column>
    <el-table-column prop="broker" label="经纪公司"></el-table-column>
    <el-table-column label="操作">
      <template #default>
        <el-button size="small">设置</el-button>
        <el-button size="small">查询</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  setup() {
    const accountList = [
      {
        account: '1035390',
        name: '西南证券期货1',
        type: '期货账号',
        profit: '100,000.00',
        todayProfit: '100,000.00',
        availableFunds: '100,000.00',
        totalAssets: '100,000.00',
        totalLiabilities: '100,000.00',
        netAssets: '100,000.00',
        department: '私募实盘',
        status: '在线',
        broker: '经纪公司1',
      },
      // ... other rows ...
    ];

    return {
      accountList,
    };
  },
});
</script>

<style scoped>
/* Add custom styles if needed */
</style>
