.typical-text-button {
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;

  i {
    margin-right: 3px;
    position: relative;
    top: -1px;
    font-size: 14px;
    color: var(--g-text-color-2) !important;
  }

  span {
    font-size: 14px;
    color: var(--g-text-color-2) !important;
  }

  &:hover {
    opacity: 1;
  }
}

.typical-search-box {
  height: 36px;
  padding: 8px 14px;
  border-radius: 100px;
  background-color: var(--g-block-bg-2);

  .el-input__wrapper {
    background-color: unset !important;
    box-shadow: unset !important;
  }
}

.typical-search-box-2 {
  height: 44px;
  padding: 12px;
  border-radius: 5px;
  background-color: var(--g-block-bg-2);

  .el-input__wrapper {
    background-color: unset !important;
    box-shadow: unset !important;
  }
}

.el-dialog {
  &.typical-dialog {
    background: var(--g-bg) !important;
    padding: 20px;
  }
}

.typical-form {
  .el-form-item {
    display: block;
    .el-form-item__content {
      line-height: 38px;
    }
    .el-input,
    .el-select__wrapper {
      height: 34px !important;
      line-height: 34px !important;
    }
    .el-date-editor {
      width: 100%;
    }
  }
}

.typical-radio-group {
  padding: 0 10px;
  border-radius: 4px;
  height: 34px !important;
  line-height: 34px !important;
  background-color: var(--g-block-bg-2);
}

.typical-tabs {
  .el-tabs__nav {
    width: 100%;
  }

  .el-tabs__item {
    flex-grow: 1;
    flex-shrink: 1;
    height: 57px !important;
    border-right: unset !important;
  }
}

.custom-confirm {
  background-color: var(--g-bg) !important;
  padding: 24px !important;
  .el-dialog__header {
    padding-bottom: 0 !important;
  }
}
