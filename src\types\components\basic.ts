/**
 * 自定义确认对话框选项
 */
export interface ConfirmDialogOption {
  title: string;
  message: string;
  width?: number | string;
  confirmText?: string;
  confirmType?: 'primary' | 'info' | 'success' | 'warning' | 'danger' | undefined;
  cancelText?: string;
  cancelType?: 'primary' | 'info' | 'success' | 'warning' | 'danger' | undefined;
}

/**
 * 自定义确认对话框服务选项
 */
export interface ConfirmDialogServiceOption extends ConfirmDialogOption {
  onConfirm?: () => void;
  onCancel?: () => void;
}

/**
 * 自定义文件上传对话框选项
 */
export interface FileUploadDialogOption {
  fileTypes?: string[];
  title?: string;
  message?: string;
  width?: number | string;
  confirmText?: string;
  cancelText?: string;
}

/**
 * 自定义文件上传对话框服务选项
 */
export interface FileUploadDialogServiceOption extends FileUploadDialogOption {
  onChoose?: () => void;
  onUpload?: () => void;
  onCancel?: () => void;
}
