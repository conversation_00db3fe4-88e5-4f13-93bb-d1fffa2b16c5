<template>
  <div>
    <!-- 流程配置 -->
    <el-table :data="processConfig" style="width: 100%">
      <el-table-column prop="name" label="流程名称"></el-table-column>
      <el-table-column prop="type" label="类型"></el-table-column>
      <el-table-column prop="node" label="流程节点"></el-table-column>
      <el-table-column prop="account" label="绑定账号"></el-table-column>
      <el-table-column label="操作">
        <template #default>
          <el-button size="small">设置</el-button>
          <el-button size="small">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 人员配置 -->
    <el-row>
      <el-col :span="12">
        <el-select v-model="queryUsers" multiple placeholder="查询员">
          <el-option
            v-for="user in users"
            :key="user.id"
            :label="user.name"
            :value="user.id"
          ></el-option>
        </el-select>
      </el-col>
      <el-col :span="12">
        <el-select v-model="riskControlUsers" multiple placeholder="风控员">
          <el-option
            v-for="user in users"
            :key="user.id"
            :label="user.name"
            :value="user.id"
          ></el-option>
        </el-select>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive } from 'vue';

export default defineComponent({
  setup() {
    const processConfig = [
      {
        name: '交易流程',
        type: '交易流程',
        node: '投资顾问 > 投顾 > 交易员',
        account: '103590、123456',
      },
      // ... other rows ...
    ];

    const users = [
      { id: 1, name: '张三' },
      // ... other users ...
    ];

    const queryUsers = reactive([]);
    const riskControlUsers = reactive([]);

    return {
      processConfig,
      users,
      queryUsers,
      riskControlUsers,
    };
  },
});
</script>

<style scoped>
/* Add custom styles if needed */
</style>
