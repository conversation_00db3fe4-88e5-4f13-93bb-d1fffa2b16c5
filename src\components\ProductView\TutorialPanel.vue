<template>
  <el-tabs v-model="activeTab" class="typical-tabs">
    <el-tab-pane :name="tabs.product">
      <template #label>
        <slot name="label">
          <span>
            <i class="iconfont icon-consist"></i>
            产品信息
          </span>
        </slot>
      </template>
      <ProductBasicInfoForm v-model="contextProduct"></ProductBasicInfoForm>
    </el-tab-pane>
    <el-tab-pane :name="tabs.account">
      <template #label>
        <slot name="label">
          <span>
            <i class="iconfont icon-block"></i>
            账号管理
          </span>
        </slot>
      </template>
      <div h-500 line-height-500 text-center>account</div>
    </el-tab-pane>
    <el-tab-pane :name="tabs.user">
      <template #label>
        <slot name="label">
          <span>
            <i class="iconfont icon-user"></i>
            人员/流程设置
          </span>
        </slot>
      </template>
      <div h-500 line-height-500 text-center>user</div>
    </el-tab-pane>
    <el-tab-pane :name="tabs.risk">
      <template #label>
        <slot name="label">
          <span>
            <i class="iconfont icon-bell"></i>
            风控设置
          </span>
        </slot>
      </template>
      <div h-500 line-height-500 text-center>risk</div>
    </el-tab-pane>
  </el-tabs>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ProductBasicInfoForm from './ProductBasicInfoForm.vue';
import type { ProductInfo } from '@/types';

const contextProduct = defineModel<ProductInfo | null>();
const tabs = { product: 'p', account: 'a', user: 'u', risk: 'r' };
const activeTab = ref(tabs.product);
</script>

<style scoped></style>
