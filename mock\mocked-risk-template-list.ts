const data = [
  {
    id: 1,
    riskTemplateName: '交易量风控模板',
    createTime: 1752558239796,
    createUserId: 1001,
    createUserriskTemplateName: '张伟',
    createUserName: '李逵',
    globalRiskTemplate: false,
    orgId: 1,
    updateTime: null,
  },
  {
    id: 2,
    riskTemplateName: '价格波动风控模板',
    createTime: 1752558239796,
    createUserId: 1001,
    createUserriskTemplateName: '王芳',
    createUserName: '李逵',
    globalRiskTemplate: false,
    orgId: 1,
    updateTime: null,
  },
  {
    id: 3,
    riskTemplateName: '持仓限制风控模板',
    createTime: 1752558239796,
    createUserId: 1001,
    createUserriskTemplateName: '李娜',
    createUserName: '李逵',
    globalRiskTemplate: false,
    orgId: 1,
    updateTime: null,
  },
  {
    id: 4,
    riskTemplateName: '信用额度风控模板',
    createTime: 1752558239796,
    createUserId: 1001,
    createUserriskTemplateName: '刘洋',
    createUserName: '李逵',
    globalRiskTemplate: false,
    orgId: 1,
    updateTime: null,
  },
  {
    id: 5,
    riskTemplateName: '交易时间窗口模板',
    createTime: 1752558239796,
    createUserId: 1001,
    createUserriskTemplateName: '陈敏',
    createUserName: '李逵',
    globalRiskTemplate: false,
    orgId: 1,
    updateTime: null,
  },
  {
    id: 6,
    riskTemplateName: '高频交易检测模板',
    createTime: 1752558239796,
    createUserId: 1001,
    createUserriskTemplateName: '杨洋',
    createUserName: '李逵',
    globalRiskTemplate: false,
    orgId: 1,
    updateTime: null,
  },
  {
    id: 7,
    riskTemplateName: '跨市场套利监控模板',
    createTime: 1752558239796,
    createUserId: 1001,
    createUserriskTemplateName: '黄娟',
    createUserName: '李逵',
    globalRiskTemplate: false,
    orgId: 1,
    updateTime: null,
  },
  {
    id: 8,
    riskTemplateName: '异常交易行为模板',
    createTime: 1752558239796,
    createUserId: 1001,
    createUserriskTemplateName: '周杰',
    createUserName: '李逵',
    globalRiskTemplate: false,
    orgId: 1,
    updateTime: null,
  },
];

export default data;
