<script setup lang="tsx" generic="T extends 'account' | 'product'">
import { computed, onBeforeUnmount, onMounted, shallowRef, useTemplateRef, watch } from 'vue';
import { ElMessage, TableV2SortOrder } from 'element-plus';
import VirtualizedTable from '../VirtualizedTable.vue';
import type { AccountInfo, ColumnDefinition, RowAction } from '@/types';
import { isOrderCancelable, OrderStatusEnum, PositionEffectEnum, TradeDirectionEnum } from '@/enum';
import { RecordService, TradingService } from '@/api';
import { Misc, Utils } from '@/script';
import type { MomFundInfo, OrderInfo, SocketDataPackage } from '../../../../../xtrade-sdk';

// 定义组件属性
const { type, activeItem } = defineProps<{
  type: T;
  activeItem?: T extends 'account' ? AccountInfo : MomFundInfo;
}>();

// 基础列定义
const baseColumns: ColumnDefinition<OrderInfo> = [
  {
    key: 'instrument',
    dataKey: 'instrument',
    title: '代码',
    width: 80,
    sortable: true,
  },
  {
    key: 'instrumentName',
    dataKey: 'instrumentName',
    title: '名称',
    width: 100,
    sortable: true,
  },
  {
    key: 'direction',
    dataKey: 'direction',
    title: '方向',
    width: 60,
    sortable: true,
    cellRenderer: ({ cellData }) => {
      const dirText = TradeDirectionEnum[cellData];
      return <span class={Utils.getColorClass(cellData)}>{dirText}</span>;
    },
  },
  {
    key: 'positionEffect',
    dataKey: 'positionEffect',
    title: '开平',
    width: 60,
    sortable: true,
    cellRenderer: ({ cellData }) => {
      return <span>{PositionEffectEnum[cellData]}</span>;
    },
  },
  {
    key: 'orderPrice',
    dataKey: 'orderPrice',
    title: '委托价',
    width: 80,
    sortable: true,
  },
  {
    key: 'volumeOriginal',
    dataKey: 'volumeOriginal',
    title: '委托量',
    width: 80,
    sortable: true,
  },
  {
    key: 'tradedVolume',
    dataKey: 'tradedVolume',
    title: '成交量',
    width: 80,
    sortable: true,
  },
  {
    key: 'tradedPrice',
    dataKey: 'tradedPrice',
    title: '成交价',
    width: 80,
    sortable: true,
  },
  {
    key: 'orderStatus',
    dataKey: 'orderStatus',
    title: '状态',
    width: 100,
    sortable: true,
    cellRenderer: ({ cellData }) => {
      const colorClass = getOrderStatusColor(cellData as number);
      return <span class={colorClass}>{OrderStatusEnum[cellData]}</span>;
    },
  },
  {
    key: 'orderTime',
    dataKey: 'orderTime',
    title: '委托时间',
    width: 160,
    sortable: true,
  },
];

// 产品特有的列
const productColumn: ColumnDefinition<OrderInfo>[0] = {
  key: 'accountName',
  dataKey: 'accountName',
  title: '账户',
  width: 120,
  sortable: true,
};

// 根据类型动态生成列
const columns = computed(() => {
  const cols = [...baseColumns];
  if (type === 'product') {
    cols.push(productColumn);
  }
  return cols;
});

// 行操作
const rowActions: RowAction<OrderInfo>[] = [
  {
    label: '撤单',
    show: (row: OrderInfo) => isOrderCancelable(row.orderStatus),
    onClick: (row: OrderInfo) => {
      TradingService.cancelOrder(row.id);
      ElMessage.success('已发送撤单请求');
    },
    type: 'var(--g-red)',
  },
];

// 过滤委托状态选项
const filterOrderStatuses = [
  { label: '全部', value: 0 },
  { label: '未完成', value: 1 },
  { label: '废单', value: 2 },
];

// 过滤委托状态
const orderStatus = shallowRef(0);

// 委托数据
const orders = shallowRef<OrderInfo[]>([]);
const tableRef = useTemplateRef('tableRef');

const customFilter = (item: OrderInfo) => {
  if (!orderStatus.value) {
    return true;
  }
  if (orderStatus.value === 1) {
    return isOrderCancelable(item.orderStatus);
  }
  if (orderStatus.value === 2) {
    return item.orderStatus === OrderStatusEnum.废单;
  }
  return true;
};

// 监听账户/产品变化，重新获取委托数据
watch(
  () => activeItem,
  newItem => {
    if (newItem) {
      fetchOrders();
    }
  },
  { deep: true },
);

// 初始化数据
onMounted(() => {
  if (activeItem) {
    fetchOrders();
  }
  TradingService.subscribeOrderChange(handleOrderChange);
});

onBeforeUnmount(() => {
  TradingService.unsubscribeOrderChange(handleOrderChange);
});

/** 监听委托变化 */
const handleOrderChange = (data: SocketDataPackage<OrderInfo>) => {
  const { body } = data;
  if (body) {
    Misc.putRow(body, orders);
  }
};

// 获取订单状态颜色
const getOrderStatusColor = (value: number): string => {
  switch (value) {
    case OrderStatusEnum.全成:
      return 'c-[var(--g-green)]';
    case OrderStatusEnum.已撤:
    case OrderStatusEnum.已驳回:
    case OrderStatusEnum.废单:
      return 'c-[var(--g-red)]';
    case OrderStatusEnum.部分成交:
    case OrderStatusEnum.部分成交撤单:
      return 'c-[var(--g-orange)]';
    default:
      return 'c-[var(--g-white)]';
  }
};

// 实际应用中的数据获取函数
const fetchOrders = async () => {
  if (!activeItem) return;
  orders.value = await RecordService.getTodayOrders(activeItem.id);
};

// 撤销选中的委托
const cancelSelectedOrders = () => {
  const selectedRows = tableRef.value?.selectedRows ?? [];

  if (selectedRows.length === 0) {
    ElMessage.warning('请选择委托');
    return;
  }

  const selectedCancelableRows = selectedRows.filter(row => isOrderCancelable(row.orderStatus));

  if (selectedCancelableRows.length === 0) {
    ElMessage.warning('无可撤销委托');
    return;
  }

  selectedCancelableRows.forEach(row => {
    TradingService.cancelOrder(row.id);
  });
  ElMessage.success('已发送撤单请求');
};

// 撤销全部委托
const cancelAllOrders = () => {
  const cancelableOrders = orders.value.filter(order => isOrderCancelable(order.orderStatus));
  if (cancelableOrders.length === 0) {
    ElMessage.warning('无可撤销委托');
    return;
  }
  cancelableOrders.forEach(order => {
    TradingService.cancelOrder(order.id);
  });
  ElMessage.success('已发送撤单请求');
};
</script>

<template>
  <VirtualizedTable
    ref="tableRef"
    :sort="{ key: 'createTime', order: TableV2SortOrder.DESC }"
    :columns
    :data="orders"
    :row-actions
    :customFilter
    select
    fixed
  >
    <template #left>
      <div flex aic>
        <el-radio-group mr-16 v-model="orderStatus">
          <el-radio v-for="status in filterOrderStatuses" :key="status.value" :value="status.value">
            {{ status.label }}
          </el-radio>
        </el-radio-group>
      </div>
    </template>
    <template #actions>
      <div class="actions" flex aic>
        <el-button @click="fetchOrders" size="small" color="var(--g-primary)">刷新</el-button>
        <el-button
          @click="cancelSelectedOrders"
          size="small"
          color="var(--g-danger)"
          :disabled="!tableRef?.selectedRows.length"
        >
          撤勾选
        </el-button>
        <el-button @click="cancelAllOrders" size="small" color="var(--g-danger)">撤全部</el-button>
      </div>
    </template>
  </VirtualizedTable>
</template>

<style scoped></style>
