/**
 * 公用格式化方法
 */
class Formatter {
  /**
   * to format date time(can be Date object, UTC timestamp, date time string) with a given pattern
   * @param source input value to be formatted
   * @param pattern output pattern string(by default = 'yyyy-MM-dd hh:mm:ss')
   */
  static formatDateTime(source: Date | number | string, pattern?: string) {
    if (!source || source == '--') {
      return '--';
    }

    /**
     * [yyyyMMdd] formatter is commonly used across the web
     */
    if (typeof source == 'number' && source.toString().length == 8) {
      source = source.toString();
    }

    if (typeof source == 'string' && /^\d{8}$/.test(source)) {
      source = `${source.substring(0, 4)}/${source.substring(4, 6)}/${source.substring(6, 8)}`;
    }

    /**
     * the minimal length is [yyyyMMdd]/8
     */
    if (typeof source == 'string' && source.length < 8) {
      return source;
    }

    if (pattern == undefined || pattern == null) {
      pattern = 'yyyy-MM-dd hh:mm:ss';
    }

    const dt = source instanceof Date ? source : new Date(source);
    const o: any = {
      'M+': dt.getMonth() + 1,
      'd+': dt.getDate(),
      'h+': dt.getHours(),
      'm+': dt.getMinutes(),
      's+': dt.getSeconds(),
      'q+': Math.floor((dt.getMonth() + 3) / 3),
      S: dt.getMilliseconds(),
    };

    if (/(y+)/.test(pattern)) {
      pattern = pattern.replace(RegExp.$1, (dt.getFullYear() + '').substr(4 - RegExp.$1.length));
    }

    for (const k in o) {
      if (new RegExp('(' + k + ')').test(pattern)) {
        pattern = pattern.replace(
          RegExp.$1,
          RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length),
        );
      }
    }

    return pattern;
  }
}

export default Formatter;
